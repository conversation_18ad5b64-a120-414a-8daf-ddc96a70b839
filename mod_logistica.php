<?php
/**
 * mod_logistica.php
 * Versión PHP del módulo de logística con integración a base de datos
 */

// Configuración de errores (para desarrollo)
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Incluir el gestor de sesión centralizado (igual que activity_dashboard.php)
require_once 'includes/session_manager.php';

// Incluir conexión a la base de datos
$inc = include("con_db.php");

// La sesión ya está iniciada por session_manager.php
// Verificar si el usuario ha iniciado sesión usando las mismas variables que activity_dashboard.php
if (!isset($_SESSION['rut']) || empty($_SESSION['rut']) || !isset($_SESSION['id_sesion']) || empty($_SESSION['id_sesion'])) {
    // Función para mostrar mensaje de sesión no iniciada
    function mostrarMensajeSesion() {
        // Almacenar la URL actual en localStorage para referencia
        echo '<!DOCTYPE html>
        <html lang="es">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Sesión Requerida</title>
            <link rel="stylesheet" href="css/bootstrap.min.css">
            <!-- Estilos movidos a tecnico_home_logis.css -->
        </head>
        <body>
            <div class="session-card">
                <h3>Sesión no encontrada</h3>
                <p class="message">La sesión no se encuentra activa o ha expirado.</p>
                <div class="btn-group">
                    <button id="btnVolver" class="btn btn-secondary">Volver Atrás</button>
                    <a href="login.php" class="btn btn-primary">Iniciar Sesión</a>
                </div>

                <div class="debug-info">
                    <p>Si estás viendo esta pantalla frecuentemente, es posible que tu sesión no se esté guardando correctamente.</p>
                    <p>Intenta las siguientes acciones:</p>
                    <ul>
                        <li>Eliminar cookies y caché del navegador</li>
                        <li>Abrir el enlace en una nueva pestaña</li>
                        <li>Usar el enlace "Iniciar Sesión" para iniciar una nueva sesión</li>
                    </ul>
                </div>
            </div>

            <script>
            // Registrar la página actual y anterior en localStorage
            if (typeof localStorage !== "undefined") {
                var currentPage = window.location.href;
                var previousPages = JSON.parse(localStorage.getItem("navigationHistory") || "[]");

                // Añadir la página actual al historial si no está ya
                if (previousPages.indexOf(currentPage) === -1) {
                    previousPages.push(currentPage);
                    // Mantener solo las últimas 5 páginas
                    if (previousPages.length > 5) {
                        previousPages = previousPages.slice(-5);
                    }
                    localStorage.setItem("navigationHistory", JSON.stringify(previousPages));
                }
            }

            // Mejorar el funcionamiento del botón volver atrás
            document.getElementById("btnVolver").addEventListener("click", function(e) {
                e.preventDefault();

                // Primero intentar con el historial del navegador
                if (window.history.length > 1) {
                    window.history.go(-1);
                    return;
                }

                // Si no hay historial de navegador, intentar con localStorage
                if (typeof localStorage !== "undefined") {
                    var previousPages = JSON.parse(localStorage.getItem("navigationHistory") || "[]");

                    if (previousPages.length > 1) {
                        // Ir a la página anterior en el historial
                        var previousPage = previousPages[previousPages.length - 2];
                        window.location.href = previousPage;
                    } else {
                        // Si no hay historial, ir a la página de inicio
                        window.location.href = "index.php";
                    }
                } else {
                    // Fallback si localStorage no está disponible
                    window.location.href = "index.php";
                }
            });
            </script>
        </body>
        </html>';
        exit;
    }

    mostrarMensajeSesion();
}

// Recuperar variables de sesión del usuario (igual que activity_dashboard.php)
$rutSesion = isset($_SESSION['rut']) ? $_SESSION['rut'] : '';
$idSesion = isset($_SESSION['id_sesion']) ? $_SESSION['id_sesion'] : '';

// Obtener información del usuario desde la base de datos usando el token de sesión
// (similar al patrón usado en otros archivos como bodega_directa.php, etc.)
$sql3 = "SELECT tut.nombre, tut.email, tla.RUT, tut.id, tut.PERFIL, tut.nombre_short, tut.area
         FROM TB_LOG_APP tla
         LEFT JOIN tb_user_tqw tut
         ON tla.RUT = tut.rut
         WHERE TOKEN = '$idSesion'";

$result = mysqli_query($conex, $sql3);
if ($result && mysqli_num_rows($result) > 0) {
    $row = mysqli_fetch_assoc($result);
    $id_usuario = $row['id'];
    $nombre_usuario = $row['nombre'];
    $nombre_short = $row['nombre_short'] ?? "";
    $rut_usuario = $row['RUT'];
    $area_usuario = $row['area'] ?? "";
    $perfil_usuario = $row['PERFIL'] ?? "";
} else {
    // Si no se encuentra el usuario en la base de datos, mostrar error
    mostrarMensajeSesion();
}

// ============================================================================
// LAZY LOADING IMPLEMENTATION - Optimización de rendimiento
// ============================================================================
// Las consultas SQL se han movido a api_mod_logis_load_table para carga bajo demanda
// Esto mejora significativamente el tiempo de carga inicial de la página

// Variables para mantener compatibilidad con el código existente
$resultado_directa = null;
$resultado_faltante = null;
$resultado_recepcion = null;
$resultado_reversa = null;

// Flag para indicar que el lazy loading está habilitado
$lazy_loading_enabled = true;

// Las consultas originales están ahora en api_mod_logis_load_table y se ejecutan
// solo cuando el usuario hace clic en las pestañas correspondientes

// Las consultas SQL originales han sido movidas a api_mod_logis_load_table
// para implementar lazy loading y mejorar el rendimiento

// Todas las consultas SQL han sido movidas a api_mod_logis_load_table para lazy loading

?>
<!DOCTYPE html>
<html lang="es">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Módulo Logística</title><!-- Updated -->

  <!-- Favicon - Comentado temporalmente para evitar errores de carga -->
  <!-- <link rel="icon" type="image/x-icon" href="img/core-img/favicon.ico"> -->

  <!-- Preload de recursos - Comentado temporalmente para evitar errores de carga -->
  <!--
  <link rel="preload" href="css/footer_modular.css" as="style">
  <link rel="preload" href="js/core/footer_modular.js" as="script">
  <link rel="preload" href="components/footer_modular.php" as="fetch" crossorigin>

  <link rel="preload" href="js/lazy-loading-enhanced.js" as="script">
  <link rel="preload" href="js/export-tables.js" as="script">
  <link rel="preload" href="js/table-config.js" as="script">
  -->
  
  <!-- Estilos base -->
  <link rel="stylesheet" href="css/activity_dashboard.css">
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
  <link rel="stylesheet" href="css/floating-menu.css">
  <link rel="stylesheet" href="css/metric-cards.css">
  <link rel="stylesheet" href="css/collapsible-sections.css">
  <link rel="stylesheet" href="css/form_panels.css">
  <link rel="stylesheet" href="css/logout-modal.css">
  <link rel="stylesheet" href="css/mod_logistica.css">
  <link rel="stylesheet" href="css/footer_modular.css"><!-- CSS del footer modular -->
  <link rel="stylesheet" href="css/floating_menu.css"><!-- CSS del menú flotante -->
  <link rel="stylesheet" href="css/footer-fix-corrected.css"><!-- Corrección para el footer (versión corregida) -->
  <link rel="stylesheet" href="css/button-fix.css"><!-- Estandarización del botón + -->

  <link rel="stylesheet" href="css/mod_logistica_responsive.css">
  <link rel="stylesheet" href="css/logout-modal.css">
  
  <!-- Todos los estilos han sido movidos a css/mod_logistica.css -->
</head>
<body class="mod-logistica dark-theme">
  <!-- Overlay para cerrar el menú flotante -->
  <div class="menu-overlay" id="menuOverlay"></div>
  <!-- Overlay para cerrar los formularios -->
  <div class="form-overlay" id="formOverlay"></div>

  <div class="app-container">
    <!-- Header Section -->
    <header class="app-header">
      <h1>Módulo Logística</h1><!-- Updated -->
    </header>

    <!-- Filtros de búsqueda -->
    <div class="search-filter-container">
      <!-- Tabs de navegación -->
      <div class="tabs-container">
        <button class="tab-button" data-tab="faltante">Faltante</button>
        <button class="tab-button active" data-tab="recepcion">Recepción</button>
        <button class="tab-button" data-tab="directa">Directa</button>
        <button class="tab-button" data-tab="reversa">Reversa</button>
      </div>
      
      <div class="d-flex justify-content-between align-items-center w-100 mt-1">
        <div class="search-box w-100 position-relative">
          <label for="searchInput" class="sr-only">Buscar en todas las tablas</label>
          <input type="text" id="searchInput" placeholder="Buscar en todas las tablas..." class="search-input" style="padding-right: 40px;">
          <button type="button" id="clearSearch" class="btn btn-clear-search position-absolute" aria-label="Limpiar búsqueda" style="right: 8px; top: 50%; transform: translateY(-50%); border: none; background: transparent; color: #6c757d; padding: 4px 8px; z-index: 10;">
            <i class="bi bi-trash"></i>
          </button>
        </div>
      </div>
    </div>

    <div class="activity-section">
      <div class="activity-metrics">
        <!-- Contenedor para todas las tablas -->
        <div class="tables-container">
          <!-- Tabla para Directa -->
          <div class="table-section" data-table="directa">
            <h3 class="table-title">Directa</h3>
            <div class="table-container">
              <table class="data-table" id="directaTable">
                <thead>
                  <tr>
                    <th class="sortable" data-sort="text">Serie <i class="sort-icon bi bi-arrow-down-up"></i></th>
                    <th class="sortable" data-sort="text">Estado <i class="sort-icon bi bi-arrow-down-up"></i></th>
                    <th>Acciones</th>
                  </tr>
                </thead>
                <tbody id="directaTableBody">
                  <!-- Lazy Loading: Los datos se cargarán dinámicamente -->
                  <tr class="loading-row">
                    <td colspan="3" class="text-center">
                      <div class="loading-container">
                        <div class="spinner-border spinner-border-sm me-2" role="status">
                          <span class="visually-hidden">Cargando...</span>
                        </div>
                        Haga clic en la pestaña "Directa" para cargar los datos
                      </div>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
          
          <!-- Tabla para Faltante -->
          <div class="table-section" data-table="faltante">
            <h3 class="table-title">Faltante</h3>
            <div class="table-container">
              <table class="data-table" id="faltanteTable">
                <thead>
                  <tr>
                    <th class="sortable" data-sort="text">Serie <i class="sort-icon bi bi-arrow-down-up"></i></th>
                    <th class="sortable" data-sort="text">Estado <i class="sort-icon bi bi-arrow-down-up"></i></th>
                    <th>Acciones</th>
                  </tr>
                </thead>
                <tbody id="faltanteTableBody">
                  <!-- Lazy Loading: Los datos se cargarán dinámicamente -->
                  <tr class="loading-row">
                    <td colspan="3" class="text-center">
                      <div class="loading-container">
                        <div class="spinner-border spinner-border-sm me-2" role="status">
                          <span class="visually-hidden">Cargando...</span>
                        </div>
                        Haga clic en la pestaña "Faltante" para cargar los datos
                      </div>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
          
          <!-- Tabla para Recepción -->
          <div class="table-section" data-table="recepcion">
            <h3 class="table-title">Recepción</h3>
            <div class="table-container">
              <table class="data-table" id="recepcionTable">
                <thead>
                  <tr>
                    <th class="sortable" data-sort="text">Serie <i class="sort-icon bi bi-arrow-down-up"></i></th>
                    <th class="sortable" data-sort="text">Estado <i class="sort-icon bi bi-arrow-down-up"></i></th>
                    <th>Acciones</th>
                  </tr>
                </thead>
                <tbody id="recepcionTableBody">
                  <!-- Lazy Loading: Los datos se cargarán dinámicamente -->
                  <tr class="loading-row">
                    <td colspan="3" class="text-center">
                      <div class="loading-container">
                        <div class="spinner-border spinner-border-sm me-2" role="status">
                          <span class="visually-hidden">Cargando...</span>
                        </div>
                        Haga clic en la pestaña "Recepción" para cargar los datos
                      </div>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
          
          <!-- Tabla para Reversa -->
          <div class="table-section" data-table="reversa">
            <h3 class="table-title">Reversa</h3>
            <div class="table-container">
              <table class="data-table" id="reversaTable">
                <thead>
                  <tr>
                    <th class="sortable" data-sort="text">Serie <i class="sort-icon bi bi-arrow-down-up"></i></th>
                    <th class="sortable" data-sort="text">Estado <i class="sort-icon bi bi-arrow-down-up"></i></th>
                    <th>Acciones</th>
                  </tr>
                </thead>
                <tbody id="reversaTableBody">
                  <!-- Lazy Loading: Los datos se cargarán dinámicamente -->
                  <tr class="loading-row">
                    <td colspan="3" class="text-center">
                      <div class="loading-container">
                        <div class="spinner-border spinner-border-sm me-2" role="status">
                          <span class="visually-hidden">Cargando...</span>
                        </div>
                        Haga clic en la pestaña "Reversa" para cargar los datos
                      </div>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>

  <!-- Offcanvas para historial -->
  <div class="offcanvas offcanvas-end" id="offcanvasHistorial" data-bs-scroll="true" tabindex="-1" aria-labelledby="offcanvasHistorialLabel">
    <div class="offcanvas-header">
      <h5 class="offcanvas-title" id="offcanvasHistorialLabel">Historial de movimiento</h5>
      <button class="btn-close text-reset" type="button" data-bs-dismiss="offcanvas" aria-label="Close"></button>
    </div>
    <div class="offcanvas-body p-4">
      <div class="mb-3">
        <label for="serieHistorial" class="form-label">SERIE</label>
        <input type="text" class="form-control" id="serieHistorial" name="serieHistorial" readonly>
      </div>

      <div class="mb-3">
        <label for="precioHistorial" class="form-label">PRECIO</label>
        <input type="text" class="form-control" id="precioHistorial" name="precioHistorial" readonly>
      </div>

      <div class="timeline-container" id="webHistorial">
        <!-- El historial se cargará dinámicamente aquí -->
      </div>
    </div>
  </div>

  <!-- End of metrics sections -->
  </div> <!-- Cierre del div.app-container -->

  <!-- Formulario de Solicitud -->
  <div class="form-panel" id="solicitudFormPanel">
    <div class="form-container">
      <div class="form-header">
        <h2 class="form-title"><i class="bi bi-clipboard-check"></i> Formulario de Solicitud</h2>
        <button type="button" class="close-form-btn" title="Cerrar formulario" aria-label="Cerrar formulario"><i class="bi bi-x-lg"></i></button>
      </div>
      <form id="solicitudForm">
        <div class="form-group">
          <label for="tipo_solicitud" class="form-label">Tipo de Solicitud</label>
          <select id="tipo_solicitud" class="form-select" required>
            <option value="" selected disabled>Seleccione el tipo de solicitud</option>
            <option value="material">Material</option>
            <option value="herramienta">Herramienta</option>
            <option value="transporte">Transporte</option>
          </select>
        </div>

        <div class="form-group">
          <label for="fecha_solicitud" class="form-label">Fecha</label>
          <input type="date" id="fecha_solicitud" class="date-input" required>
        </div>

        <div class="form-group">
          <label for="solicitante" class="form-label">RUT del Solicitante</label>
          <input type="text" id="solicitante" class="form-control" placeholder="Ingrese el RUT del solicitante" required>
        </div>

        <div class="form-group">
          <label for="descripcion_solicitud" class="form-label">Descripción</label>
          <textarea id="descripcion_solicitud" class="form-textarea" placeholder="Ingrese los detalles de la solicitud" required></textarea>
        </div>

        <div class="form-buttons">
          <button type="button" class="btn-cancel">Cancelar</button>
          <button type="submit" class="btn-submit">Enviar</button>
        </div>
      </form>
    </div>
  </div>

  <!-- Navegación inferior -->
  <nav class="bottom-nav" id="bottom-nav">
    <!-- Iconos a la izquierda -->
    <a href="activity_dashboard.php" class="nav-item">
      <i class="bi bi-grid"></i>
    </a>
    <a href="charts_dashboard.php" class="nav-item">
      <i class="bi bi-activity"></i>
    </a>
    <a href="calidad_reactiva.php" class="nav-item">
      <i class="bi bi-file-text"></i>
    </a>

    <!-- Botón + central -->
    <div class="add-button-container">
      <div class="floating-menu" id="floatingMenu">
        <a href="#" class="floating-menu-item" data-form-id="materiales">
          <i class="bi bi-box-seam"></i> Solicitud de Materiales
        </a>
      </div>
      <div class="nav-item add-button" id="addButton">
        <i class="bi bi-plus"></i>
      </div>
    </div>

    <!-- Iconos a la derecha -->
    <a href="javascript:void(0)" class="nav-item" id="ticketButton">
      <i class="bi bi-ticket"></i>
    </a>
    <a href="Tecnico_Home_LOGIS_TEST_V2.php" class="nav-item">
      <i class="bi bi-house"></i>
    </a>
    <a href="logout.php" class="nav-item" id="logoutButton">
      <i class="bi bi-box-arrow-right"></i>
    </a>
  </nav>

  <!-- Overlays necesarios para el footer modular -->
  <div class="menu-overlay" id="menuOverlay"></div>
  <div class="form-overlay" id="formOverlay"></div>

  <!-- Contenedor de formularios dinámicos -->
  <div id="formsContainer">
    <div class="form-panel" id="form-materiales" data-form-id="materiales">
      <div class="form-placeholder">Formulario de materiales no encontrado</div>
    </div>
  </div>

  <!-- Offcanvas TRANSFERENCIA (Canvas 7) -->
  <div class="offcanvas offcanvas-end" id="offcanvasrigh" data-bs-scroll="true" tabindex="-1"
      aria-labelledby="affanOffcanvsLabel" style="z-index: 1045;" data-bs-backdrop="true">
      <button class="btn-close btn-close-white text-reset" type="button" data-bs-dismiss="offcanvas"
          aria-label="Close"></button>

      <div class="offcanvas-body p-4">
          <h5>Canal de requerimientos</h5>

          <div class="mb-3">
              <label for="serie_tran" class="form-label">SERIE SELECCIONADA</label>
              <input type="text" class="form-control" id="serie_tran" name="serie_tran" value="" readonly required>
          </div>

          <h5>ESCALAMIENTO A SUPERVISOR</h5>

          <a class="btn m-1 btn-info" href="#" id="justificarLink">
              <i class="bi bi-cursor"></i> Validación supervisor
          </a>

          <a class="btn m-1 btn-info" href="#" id="bodegaSistemico">
              <i class="bi bi-cursor"></i> PROBLEMA SISTEMICO
          </a>

          <a class="btn m-1 btn-info" href="#" id="bodegaSeriIncorrecta">
              <i class="bi bi-cursor"></i> Equipo serie incorrecta
          </a>

          <h5 style="MARGIN-TOP: 15px;"> ESCALAMIENTO A BODEGA </h5>

          <div class="d-flex flex-column gap-2">

              <a class="btn btn-danger text-wrap d-flex align-items-center gap-2" href="#" id="bodegaLink">
                  <i class="bi bi-cursor"></i>
                  <span>Equipo con desperfecto</span>
              </a>

              <a class="btn btn-danger text-wrap d-flex align-items-center gap-2" href="#" id="bodegaLinkTOA">
                  <i class="bi bi-cursor"></i>
                  <span>Serie no aparece en TOA</span>
              </a>

              <a class="btn btn-danger text-wrap d-flex align-items-center gap-2" href="#" id="fotoCierreInv">
                  <i class="bi bi-cursor"></i>
                  <span>Serie a regularizar por cierre de inventario</span>
              </a>

              <a class="btn btn-danger text-wrap d-flex align-items-center gap-2" href="#" id="DevueltoBodega">
                  <i class="bi bi-cursor"></i>
                  <span>Devuelto a bodega</span>
              </a>

          </div>

          <h5 style="MARGIN-TOP: 15px;"> TRANSFERENCIA ENTRE TECNICOS </h5>

          <a class="btn m-1 btn-warning" style="margin-bottom:10px;margin-top:10px;" href="#" id="tecnicoLink">
              <i class="bi bi-cursor"></i> Transferencia a otro tecnico
          </a>

          <!-- Etiqueta hr como separador -->
          <hr class="separador">

          <div class="mb-3" id="tecnicoTransf" style="display: none;">
              <label for="tecnico_select" class="form-label">Técnico a quien transfiere <span class="text-danger">*</span></label>
              <select class="form-select form-select-sm form-control-clicked" id="usuario_destino" name="usuario_destino" required>
                  <option value="">Seleccione el técnico a transferir</option>
                  <!-- Opciones de técnicos se cargarán dinámicamente -->
              </select>
              <div class="invalid-feedback" id="usuario_destino_error">
                  Por favor seleccione un técnico de destino.
              </div>
          </div>

          <input type="hidden" id="usuario_destino_hidden" name="usuario_destino_hidden">

          <div class="mb-3" id="serie_tran_contain" style="display: none;">
              <label for="serie_wrong_directa" class="form-label">INGRESE LA SERIE <span class="text-danger">*</span></label>
              <input type="text" class="form-control" id="serie_tran_new" name="serie_tran_new" value="" required
                     placeholder="Ingrese la serie del equipo">
              <div class="invalid-feedback" id="serie_tran_new_error">
                  Por favor ingrese una serie válida.
              </div>
          </div>

          <div id="divArchivo" class="form-group" style="display: none;">
              <label class="form-label" for="customFile3">Cargar Foto <span class="text-muted">(Opcional)</span></label>
              <input class="form-control" name="fileInventario" id="fileInventario" type="file"
                     accept="image/*,.pdf,.doc,.docx">
              <div class="form-text">Formatos permitidos: JPG, PNG, PDF, DOC, DOCX. Tamaño máximo: 5MB</div>
              <div class="invalid-feedback" id="fileInventario_error">
                  El archivo seleccionado no es válido.
              </div>
          </div>

          <div class="mb-3" id="motivo_tran_contain" style="display: none;">
              <label id="serie_incorrecta_directa" for="motivo_tran" class="form-label">MOTIVO <span class="text-danger">*</span></label>
              <textarea class="form-control" id="motivo_tran" name="motivo_tran" rows="4" required
                        placeholder="Describa el motivo de la transferencia"></textarea>
              <div class="invalid-feedback" id="motivo_tran_error">
                  Por favor ingrese un motivo válido (mínimo 10 caracteres).
              </div>
          </div>

          <!-- Agregar el combo box -->
          <div class="mb-3" id="motivoASuper" style="display: none;">
              <label class="form-label" for="defaultSelectSm">Seleccionar motivo <span class="text-danger">*</span></label>
              <select class="form-select form-select-sm form-control-clicked" id="defaultSelectSm"
                  name="defaultSelectSm" aria-label="Default select example" required>
                  <option value="" selected>SELECCIONAR</option>
                  <option value="perdidaTecnico">PERDIDA MATERIAL POR TECNICO</option>
                  <option value="robo">ROBO</option>
                  <option value="dano">DAÑO EN EQUIPO</option>
                  <option value="falla_tecnica">FALLA TÉCNICA</option>
                  <option value="otro">OTRO MOTIVO</option>
              </select>
              <div class="invalid-feedback" id="defaultSelectSm_error">
                  Por favor seleccione un motivo.
              </div>
          </div>
          
          <div class="mb-3">
              <input type="hidden" class="form-control" id="val_tecnico_destino" name="val_tecnico_destino">
          </div>

          <button type="button" class="btn btn-success" id="transferButton">Enviar requerimiento</button>
      </div>
  </div>

  <!-- Offcanvas INSTALACION de serie (Canvas 8) -->
  <div class="offcanvas offcanvas-end" id="offcanvasInstala" data-bs-scroll="true" tabindex="-1"
      aria-labelledby="affanOffcanvsLabel">

      <button class="btn-close btn-close-white text-reset" type="button" data-bs-dismiss="offcanvas"
          aria-label="Close"></button>

      <div class="offcanvas-body p-4">
          <h5>declara la serie instalada</h5>

          <form method="POST">
              <div class="mb-3">
                  <label for="serie_insta" class="form-label">SERIE SELECCIONADA</label>
                  <input type="text" class="form-control" placeholder="Ingrese la serie" id="serie_insta"
                      name="serie_insta" value="" required readonly>
              </div>

              <div class="mb-3">
                  <label for="formOTinsta" class="form-label">Orden de trabajo <span class="text-danger">*</span></label>
                  <input type="text" class="form-control" placeholder="Ingrese la orden de trabajo" id="formOT_insta"
                      name="formOT_insta" value="" required pattern="[0-9A-Za-z\-]+"
                      title="Solo se permiten números, letras y guiones">
                  <div class="invalid-feedback" id="formOT_insta_error">
                      Por favor ingrese una orden de trabajo válida.
                  </div>
              </div>

              <div class="mb-3">
                  <label for="rut_insta" class="form-label">RUT cliente <span class="text-muted">(Opcional)</span></label>
                  <input type="text" class="form-control" placeholder="Ej: 12345678-9" id="rut_insta"
                      name="rut_insta" value="" pattern="[0-9]{7,8}-[0-9kK]{1}"
                      title="Formato: 12345678-9">
                  <div class="form-text">Formato: 12345678-9 (opcional)</div>
                  <div class="invalid-feedback" id="rut_insta_error">
                      Por favor ingrese un RUT válido (formato: 12345678-9).
                  </div>
              </div>

              <div class="form-group">
                  <label class="form-label" for="obs_insta">Observaciones <span class="text-danger">*</span></label>
                  <textarea class="form-control" id="obs_insta"
                      placeholder="Describa los detalles de la instalación" name="obs_insta" cols="3" rows="5"
                      required minlength="10" maxlength="500"></textarea>
                  <div class="form-text">Mínimo 10 caracteres, máximo 500 caracteres</div>
                  <div class="invalid-feedback" id="obs_insta_error">
                      Por favor ingrese observaciones válidas (mínimo 10 caracteres).
                  </div>
              </div>

              <div class="form-group">
                  <label class="form-label" for="fileInsta">Cargar Archivo <span class="text-muted">(Opcional)</span></label>
                  <input class="form-control" name="fileInsta" id="fileInsta" type="file"
                         accept="image/*,.pdf,.doc,.docx">
                  <div class="form-text">Formatos permitidos: JPG, PNG, PDF, DOC, DOCX. Tamaño máximo: 5MB</div>
                  <div class="invalid-feedback" id="fileInsta_error">
                      El archivo seleccionado no es válido.
                  </div>
              </div>

              <button type="button" class="btn btn-success" id="instalButton">Declarar instalada</button>
          </form>
      </div>
  </div>

  <!-- Offcanvas TRANSFERENCIA REVERSA (Canvas 9) -->
  <div class="offcanvas offcanvas-end" id="offcanvasrevSuper" data-bs-scroll="true" tabindex="-1"
      aria-labelledby="affanOffcanvsLabel">

      <button class="btn-close btn-close-white text-reset" type="button" data-bs-dismiss="offcanvas"
          aria-label="Close"></button>

      <div class="offcanvas-body p-4">
          <h5>Validación supervisor en reversa</h5>

          <div class="mb-3">
              <label for="serie_tran" class="form-label">SERIE SELECCIONADA</label>
              <input type="text" class="form-control" id="serie_trans_rever" name="serie_trans_rever" value=""
                  readonly required>
          </div>

          <div class="mb-3">
              <label for="rutReversa" class="form-label">RUT <span class="text-muted">(Opcional)</span></label>
              <input type="text" class="form-control" id="rutReversa" name="rutReversa"
                     placeholder="Ej: 12345678-9" pattern="[0-9]{7,8}-[0-9kK]{1}"
                     title="Formato: 12345678-9">
              <div class="form-text">Formato: 12345678-9 (opcional)</div>
              <div class="invalid-feedback" id="rutReversa_error">
                  Por favor ingrese un RUT válido (formato: 12345678-9).
              </div>
          </div>

          <div class="mb-3">
              <label for="ordenReversa" class="form-label">ORDEN DE TRABAJO <span class="text-danger">*</span></label>
              <input type="text" class="form-control" id="ordenReversa" name="ordenReversa" required
                     placeholder="Ingrese la orden de trabajo" pattern="[0-9A-Za-z\-]+"
                     title="Solo se permiten números, letras y guiones">
              <div class="invalid-feedback" id="ordenReversa_error">
                  Por favor ingrese una orden de trabajo válida.
              </div>
          </div>

          <div class="mb-3">
              <label for="super_destino" class="form-label">Transferencia supervisor <span class="text-danger">*</span></label>
              <input type="text" class="form-control" id="super_destino" name="super_destino"
                  value="" list="list_tecnico" readonly required placeholder="Se asignará automáticamente">
              <div class="invalid-feedback" id="super_destino_error">
                  Por favor asigne un supervisor de destino.
              </div>
          </div>

          <div class="form-group mb-3">
              <label class="form-label" id="label_motivo" for="listReversa">Seleccionar motivo <span class="text-danger">*</span></label>
              <select class="form-select form-select-sm form-control-clicked" id="listReversa" name="listReversa"
                  aria-label="Seleccionar motivo" required>
                  <option value="" selected>SELECCIONAR</option>
                  <option value="perdidaTecnico">PERDIDA DE CLIENTE</option>
                  <option value="robo">ROBO</option>
                  <option value="Serie Incorrecta">SERIE EQUIPO INCORRECTA</option>
                  <option value="serieMalDesprovisionada">SERIE MAL DESPROVISIONADA</option>
                  <option value="serieInstalada">Equipo instalado en reversa</option>
                  <option value="dano_equipo">DAÑO EN EQUIPO</option>
                  <option value="falla_tecnica">FALLA TÉCNICA</option>
              </select>
              <div class="invalid-feedback" id="listReversa_error">
                  Por favor seleccione un motivo.
              </div>
          </div>

          <div class="mb-3">
              <label for="serieNewReversa" class="form-label">SERIE FISICA RETIRADA <span class="text-danger">*</span></label>
              <input type="text" class="form-control" placeholder="Ingrese la serie física retirada"
                     id="serieNewReversa" name="serieNewReversa" required
                     pattern="[A-Za-z0-9\-]+" title="Solo se permiten letras, números y guiones">
              <div class="invalid-feedback" id="serieNewReversa_error">
                  Por favor ingrese una serie válida.
              </div>
          </div>

          <div class="form-group">
              <label class="form-label" for="obs_rev_tra">Observaciones <span class="text-danger">*</span></label>
              <textarea class="form-control" id="obs_rev_tra" name="obs_rev_tra" cols="3" rows="5"
                  required minlength="10" maxlength="500"
                  placeholder="Describa los detalles de la transferencia reversa"></textarea>
              <div class="form-text">Mínimo 10 caracteres, máximo 500 caracteres</div>
              <div class="invalid-feedback" id="obs_rev_tra_error">
                  Por favor ingrese observaciones válidas (mínimo 10 caracteres).
              </div>
          </div>

          <div class="form-group">
              <label class="form-label" for="userfile">Cargar Archivo <span class="text-muted">(Opcional)</span></label>
              <input class="form-control" name="userfile" id="userfile" type="file"
                     accept="image/*,.pdf,.doc,.docx">
              <div class="form-text">Formatos permitidos: JPG, PNG, PDF, DOC, DOCX. Tamaño máximo: 5MB</div>
              <div class="invalid-feedback" id="userfile_error">
                  El archivo seleccionado no es válido.
              </div>
          </div>

          <button type="button" class="btn btn-warning" id="transferReversaButton">Solicitar requerimiento</button>
      </div>
  </div>

  <!-- Offcanvas ENTREGA DE REVERSA (Canvas 10) -->
  <div class="offcanvas offcanvas-end" id="offcanvasReversaDeclara" data-bs-scroll="true" tabindex="-1"
      aria-labelledby="affanOffcanvsLabel">

      <button class="btn-close btn-close-white text-reset" type="button" data-bs-dismiss="offcanvas"
          aria-label="Close"></button>

      <div class="offcanvas-body p-4">
          <h5>Declaración de entrega para reversa</h5>

          <div class="mb-3">
              <label for="serie_trans_rever" class="form-label">SERIE SELECCIONADA</label>
              <input type="text" class="form-control" id="serieReversaDeclara" name="serie_trans_rever" value=""
                  readonly required>
          </div>

          <div class="form-group">
              <label class="form-label" for="fileReversaDecla">Cargar Archivo <span class="text-danger">*</span></label>
              <input class="form-control" name="fileReversaDecla" id="fileReversaDecla" type="file"
                     accept="image/*,.pdf,.doc,.docx" required>
              <div class="form-text">Formatos permitidos: JPG, PNG, PDF, DOC, DOCX. Tamaño máximo: 5MB</div>
              <div class="invalid-feedback" id="fileReversaDecla_error">
                  Por favor seleccione un archivo válido.
              </div>
          </div>

          <button type="button" class="btn btn-warning" id="reversaDeclaraButton">Declarar entregada</button>
      </div>
  </div>

  <!-- Modal para Rechazo de Material -->
  <div id="popup-container" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background-color: rgba(0,0,0,0.5); z-index: 1050; align-items: center; justify-content: center;">
      <div id="popup" style="background: white; padding: 20px; border-radius: 8px; max-width: 400px; width: 90%; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
          <h2 style="margin-bottom: 20px; color: #d63384;">Gestión de rechazo</h2>
          
          <input type="hidden" id="popupSerial">
          <input type="hidden" id="popupTicket">
          <input type="hidden" id="popupIdTecnicoDestino">
          <input type="hidden" id="popupAccion">
          
          <div style="margin-bottom: 15px;">
              <label for="motivoRechazo" style="display: block; margin-bottom: 5px; font-weight: bold;">Motivo del rechazo:</label>
              <select id="motivoRechazo" style="width: 100%; padding: 8px; border: 1px solid #ccc; border-radius: 4px;" required>
                  <option value="" selected disabled>Seleccione un motivo</option>
                  <option value="Serie fisica no entregada">Serie física no entregada</option>
                  <option value="Serie no corresponde">Serie no corresponde</option>
              </select>
          </div>
          
          <div style="text-align: center; margin-top: 20px;">
              <button onclick="Rechazoaceptar()" style="background-color: #d63384; color: white; border: none; padding: 10px 20px; margin-right: 10px; border-radius: 4px; cursor: pointer;">Aceptar</button>
              <button onclick="Rechazocancelar()" style="background-color: #6c757d; color: white; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer;">Cancelar</button>
          </div>
      </div>
  </div>

  <!-- Configuración global para módulo de logística -->
  <script>
    // Asegurar que el título sea correcto
    document.title = "Módulo Logística";
    
    // Configuración global para el módulo de logística
    window.ModLogisticaConfig = {
      endpoints: {
        main: 'GET_LOGISTICA.php',
        historial: 'GET_LOGISTICA.php',
        tecnicos: 'GET_LOGISTICA.php?accion=lista_tecnicos',
        precio: 'GET_LOGISTICA.php',
        actualizarTabla: 'GET_LOGISTICA.php'
      },
      user: {
        id: <?php echo json_encode($id_usuario); ?>,
        nombre: <?php echo json_encode($nombre_usuario); ?>,
        rut: <?php echo json_encode($rut_usuario); ?>,
        nombreShort: <?php echo json_encode($nombre_short); ?>,
        area: <?php echo json_encode($area_usuario); ?>,
        perfil: <?php echo json_encode($perfil_usuario); ?>
      },
      debug: <?php echo json_encode(true); ?> // Para desarrollo
    };
  </script>

  <!-- Scripts -->
  <script>
    // Declarar variable global userId para los scripts (solo si no está ya declarada)
    if (typeof userId === 'undefined') {
      var userId = <?php echo $id_usuario; ?>;
    }
  </script>
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

  <!-- Módulos de logística (cargados en orden de dependencias) -->
  <!-- COMENTADO HASTA QUE SE SUBAN LOS ARCHIVOS AL SERVIDOR DE PRODUCCIÓN -->
  <!--
  <script src="js/mod_logistica/config.js" defer></script>
  <script src="js/mod_logistica/validation.js" defer></script>
  <script src="js/mod_logistica/ajax-handlers.js" defer></script>
  <script src="js/mod_logistica/form-handlers.js" defer></script>
  <script src="js/mod_logistica/table-handlers.js" defer></script>
  <script src="js/mod_logistica/mod_logistica.js" defer></script>
  -->
  <script src="js/activity_dashboard.js"></script>
  <script src="js/collapsible-sections.js"></script>
  <script src="js/core/footer_modular.js"></script>
  
  <!-- Sistema de Lazy Loading v2 - TEMPORALMENTE COMENTADO DEBIDO A ERRORES 404 -->
  <!--
  <script src="js/lazy-loading-enhanced.js"></script>
  <script src="js/export-tables.js"></script>
  <script src="js/table-config.js"></script>


  <script src="js/stagewise-init.js"></script>


  <script src="js/historial_loader.js"></script>


  <script src="js/lazy_loading_compatibility.js"></script>
  -->

  <!-- COMENTADO TEMPORALMENTE PARA EVITAR CONFLICTOS CON EL SISTEMA DE LAZY LOADING -->
  <!-- <script src="js/mod_logistica_tables.js"></script> -->

  <!-- Script personalizado para mejorar el comportamiento del botón aceptar -->
  <script src="js/mod_logistica_custom.js"></script>
  <script src="js/logout_modal.js"></script>

  
  
  <!-- El JavaScript para navegación por pestañas ahora está en los módulos -->

  <!-- Todo el JavaScript ahora está modularizado en archivos separados -->




  <!-- Módulos JavaScript del Sistema de Logística -->
  <!-- Cargar módulos en orden de dependencias -->
  <script src="js/mod_logistica/config.js"></script>
  <script src="js/mod_logistica/api.js"></script>
  <script src="js/mod_logistica/ui.js"></script>
  <script src="js/mod_logistica/tables.js"></script>
  <script src="js/mod_logistica/handlers.js"></script>
  <script src="js/mod_logistica/main.js"></script>


  <!-- Script de compatibilidad para mantener funciones globales -->
  <script>
    console.log('🚀 Sistema Modular de Logística iniciado');

    // Los módulos se han cargado por separado para mejor organización y mantenimiento

    console.log('✅ Módulos JavaScript del Sistema de Logística cargados correctamente');
  </script>

  <?php
  // Incluir el script para mantener la sesión activa (igual que activity_dashboard.php)
  incluirScriptSesion();
  ?>
  
  <!-- El script personalizado ya se carga en la sección de scripts -->
</body>
</html>