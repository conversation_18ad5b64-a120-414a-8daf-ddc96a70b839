/* =================================== */
/*     Estilos Responsivos: <PERSON><PERSON><PERSON><PERSON>     */
/* =================================== */

/* Aumentar tamaño de botones de acción solo en móviles */
@media (max-width: 767px) {
  .action-buttons-container .action-btn {
    transform: scale(1.15);
    margin: 0 4px;
  }
}

/* Responsive para diferentes tamaños de pantalla */
@media (min-width: 768px) {
  body.mod-logistica .app-container {
    max-width: 480px;
    margin: 0 auto;
    padding: 20px;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  }

  .tabs-container {
    flex-wrap: nowrap;
    justify-content: space-between;
  }

  .tab-button {
    flex: 1;
    max-width: none;
  }
  
  body.mod-logistica .data-table th,
  body.mod-logistica .data-table td {
    padding: 15px 20px;
  }
  
  .bottom-nav {
    max-width: 480px;
    left: 50%;
    transform: translateX(-50%);
    border-bottom-left-radius: 20px;
    border-bottom-right-radius: 20px;
  }
}

/* Ajustes para móviles */
@media (max-width: 767px) {
  body.mod-logistica {
    overflow-x: hidden !important;
    font-size: 1.3rem; /* Base font size increased by 30% (20% + 10%) */
  }
  
  /* Increase text sizes in tables - 30% larger (20% + 10%) */
  .tables-container,
  .tables-container table,
  .tables-container th,
  .tables-container td,
  .table-title,
  .data-table,
  .data-table th,
  .data-table td {
    font-size: 1.3em !important; /* 30% larger than parent */
  }
  
  /* Adjust tab buttons - 1px smaller than before */
  .tab-button {
    font-size: 0.95em !important;
    padding: 0.5em 0.8em !important;
  }
  
  /* Adjust action buttons */
  .action-btn,
  .export-buttons .action-btn,
  .section-export-btn {
    font-size: 1.05em !important;
    padding: 0.7em 1em !important;
  }
  
  /* Adjust icon sizes in buttons */
  .action-btn i,
  .tab-button i {
    font-size: 1.05em !important;
  }

  body.mod-logistica .app-container {
    width: 100vw !important;
    max-width: 100vw !important;
    margin: 0 !important;
    padding: 8px !important;
    box-sizing: border-box !important;
  }

  body.mod-logistica .activity-section {
    width: 100% !important;
    margin: 0 0 80px 0 !important;
    padding: 0 !important;
  }

  body.mod-logistica .activity-metrics {
    width: 100% !important;
    margin: 0 !important;
    padding: 0 !important;
  }

  .tabs-container {
    flex-wrap: wrap;
    justify-content: center;
    gap: 6px;
  }

  .tab-button {
    flex: 0 1 auto;
    min-width: calc(50% - 8px);
    max-width: calc(50% - 8px);
    font-size: 12px;
    padding: 8px 12px;
    text-align: center;
  }

  body.mod-logistica .data-table th {
    background: rgba(0, 225, 253, 0.1) !important;
    color: #00e1fd !important;
    padding: 8px 6px !important;
    text-align: left !important;
    font-weight: 600 !important;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
    font-size: 10px !important;
  }

  body.mod-logistica .data-table td {
    padding: 8px 6px !important;
    border-bottom: 1px solid rgba(255, 255, 255, 0.05) !important;
    font-size: 11px !important;
  }

  body.mod-logistica .data-table tr:hover {
    background: rgba(255, 255, 255, 0.03) !important;
  }

  /* Redistribución de columnas para móviles */
  body.mod-logistica .data-table th:nth-child(1) { width: 30%; } /* Serie */
  body.mod-logistica .data-table th:nth-child(2) { width: 15%; } /* Tipo */
  body.mod-logistica .data-table th:nth-child(3) { width: 25%; } /* Estado */
  body.mod-logistica .data-table th:nth-child(4) { width: 15%; } /* Fecha */
  body.mod-logistica .data-table th:nth-child(5) { width: 15%; } /* Acciones */

  /* Responsive para mostrar/ocultar contenido */
  body.mod-logistica .type-full,
  body.mod-logistica .date-full {
    display: none !important;
  }

  body.mod-logistica .type-short,
  body.mod-logistica .date-short {
    display: inline !important;
  }

  /* Estilos para badges de estado */
  body.mod-logistica .status-badge {
    padding: 2px 6px !important;
    border-radius: 12px !important;
    font-size: 9px !important;
    font-weight: 600 !important;
    text-transform: uppercase !important;
    min-width: 45px !important;
  }

  /* Contenedor de botones de acción para móviles */
  body.mod-logistica .action-buttons-container {
    display: flex !important;
    gap: 2px !important;
    justify-content: center !important;
    align-items: center !important;
    flex-wrap: nowrap !important;
  }

  /* Botones de acción para móviles - mantener consistencia con el diseño desktop */
  body.mod-logistica .action-btn {
    background: rgba(255, 255, 255, 0.08) !important;
    border: 1px solid rgba(255, 255, 255, 0.15) !important;
    color: rgba(255, 255, 255, 0.8) !important;
    cursor: pointer !important;
    padding: 6px 8px !important;
    border-radius: 6px !important;
    font-size: 11px !important;
    margin: 0 1px !important;
    min-width: 28px !important;
    height: 28px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    transition: all 0.15s ease !important;
  }

  /* Efectos hover para móviles */
  body.mod-logistica .action-btn:hover {
    background: rgba(0, 225, 253, 0.15) !important;
    border-color: rgba(0, 225, 253, 0.3) !important;
    color: #00e1fd !important;
    transform: translateY(-1px) !important;
  }

  /* Botones específicos - mantener los mismos colores que desktop */
  body.mod-logistica .action-btn.historial-btn {
    background: rgba(255, 255, 255, 0.08) !important;
    border: 1px solid rgba(255, 255, 255, 0.15) !important;
    color: rgba(255, 255, 255, 0.8) !important;
  }

  body.mod-logistica .action-btn.declarar-btn {
    background: rgba(255, 255, 255, 0.08) !important;
    border: 1px solid rgba(255, 255, 255, 0.15) !important;
    color: rgba(255, 255, 255, 0.8) !important;
  }

  body.mod-logistica .action-btn.justificar-btn,
  body.mod-logistica .action-btn.transfiere-btn {
    background: rgba(255, 255, 255, 0.08) !important;
    border: 1px solid rgba(255, 255, 255, 0.15) !important;
    color: rgba(255, 255, 255, 0.8) !important;
  }

  /* Botones específicos de recepción */
  body.mod-logistica .action-btn.aceptar-btn {
    background: rgba(40, 167, 69, 0.15) !important;
    border: 1px solid rgba(40, 167, 69, 0.3) !important;
    color: #28a745 !important;
  }

  /* Botones de reversa */
  body.mod-logistica .action-btn.declarar-rev-btn,
  body.mod-logistica .action-btn.transferir-rev-btn {
    background: rgba(255, 255, 255, 0.08) !important;
    border: 1px solid rgba(255, 255, 255, 0.15) !important;
    color: rgba(255, 255, 255, 0.8) !important;
  }

  /* Estilos móviles para el offcanvas de historial */
  body.mod-logistica .offcanvas-body {
    padding: 15px !important;
  }

  body.mod-logistica .offcanvas-body h5 {
    font-size: 1.1rem !important;
    margin-bottom: 15px !important;
  }

  body.mod-logistica .form-label {
    font-size: 12px !important;
  }

  body.mod-logistica .form-control {
    font-size: 12px !important;
    padding: 8px 10px !important;
  }

  body.mod-logistica .timeline-container {
    max-height: 300px !important;
  }

  body.mod-logistica .card-item {
    padding: 10px !important;
    margin: 6px 0 !important;
  }

  body.mod-logistica .card-date {
    font-size: 10px !important;
    padding: 3px 6px !important;
  }

  body.mod-logistica .card-info {
    font-size: 11px !important;
  }

  /* Reducir ancho del offcanvas en móviles */
  body.mod-logistica .offcanvas-end {
    width: 85% !important;
    max-width: 320px !important;
  }

  /* Ajustes para títulos de tabla en móvil - mantener consistencia con desktop */
  .table-title {
    padding: 15px 20px !important;
    font-size: 1.2rem !important;
    color: #ffffff !important;
    background: linear-gradient(135deg, #0077b6 0%, #023e8a 100%) !important;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    margin: 0 !important;
  }
}

/* Media query para pantallas extra pequeñas */
@media (max-width: 360px) {
  body.mod-logistica .data-table th,
  body.mod-logistica .data-table td {
    padding: 6px 4px !important;
    font-size: 10px !important;
  }

  body.mod-logistica .status-badge {
    font-size: 8px !important;
    padding: 2px 4px !important;
    min-width: 35px !important;
  }

  /* Botones de acción para pantallas extra pequeñas */
  body.mod-logistica .action-buttons-container {
    gap: 1px !important;
  }

  body.mod-logistica .action-btn {
    padding: 2px 3px !important;
    font-size: 8px !important;
    margin: 0 !important;
    min-width: 20px !important;
    height: 20px !important;
  }

  .tab-button {
    min-width: calc(50% - 6px);
    max-width: calc(50% - 6px);
    padding: 6px 8px;
    font-size: 11px;
    text-align: center;
  }

  .tabs-container {
    gap: 4px;
  }

  .table-title {
    padding: 12px 15px !important;
    font-size: 1.1rem !important;
    color: #ffffff !important;
    background: linear-gradient(135deg, #0077b6 0%, #023e8a 100%) !important;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    margin: 0 !important;
  }

  /* Estilos para pantallas extra pequeñas - offcanvas historial */
  body.mod-logistica .offcanvas-body {
    padding: 12px !important;
  }

  body.mod-logistica .offcanvas-body h5 {
    font-size: 1rem !important;
    margin-bottom: 12px !important;
  }

  body.mod-logistica .timeline-container {
    max-height: 250px !important;
  }

  body.mod-logistica .card-item {
    padding: 8px !important;
    margin: 4px 0 !important;
  }

  body.mod-logistica .card-date {
    font-size: 9px !important;
    padding: 2px 4px !important;
  }

  body.mod-logistica .card-info {
    font-size: 10px !important;
  }

  /* Ancho del offcanvas para pantallas extra pequeñas */
  body.mod-logistica .offcanvas-end {
    width: 90% !important;
    max-width: 280px !important;
  }
}

@keyframes spin {
  0% { transform: translateY(-50%) rotate(0deg); }
  100% { transform: translateY(-50%) rotate(360deg); }
}
