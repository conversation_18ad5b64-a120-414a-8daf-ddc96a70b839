// Sistema de carga de historial de logística
// Archivo: js/historial_loader.js

// Función para cargar el historial usando fetch API
function cargarHistorial(serie) {
    // Limpiar el contenido del elemento web antes de enviar la solicitud
    document.getElementById('webHistorial').innerHTML = '';
    
    fetch(`GET_LOGISTICA.php?proceso=historial2&serie=${serie}`, {
        method: 'GET',
        headers: {
            'Content-Type': 'application/json'
        }
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(response.statusText);
        }
        return response.json();
    })
    .then(data => {
        // Procesa la data y renderiza los elementos.
        let html = '';
        if (data && data.length > 0) {
            data.forEach(item => {
                html += `<div class="card-item">
                    <div class="card-content">
                        <span class="card-date">${item.fecha_hora}</span>
                        <p class="card-info">Movimiento: ${item.Semantica}</p>
                        <p class="card-info">Desde: ${item.Nombre_origen || 'N/A'}</p>`;

                if (item.movimiento_historial) {
                    html += `<p class="card-info">Hacia: ${item.Nombre_destino || 'N/A'}</p>`;
                } else {
                    html += `<p class="card-info">Hacia: ${item.Nombre_destino || 'N/A'}</p>`;
                }

                if (item.motivo) {
                    html += `<p class="card-info">Motivo: ${item.motivo}</p>`;
                }
                
                if (item.observacion) {
                    let textoCorto = item.observacion.substring(0, 30);
                    let textoMostrar = (item.observacion.length > 30) ? textoCorto + '...' : textoCorto;
                    html += `<p class="card-info" title="${item.observacion}">Observación: ${textoMostrar}</p>`;
                }
                
                if (item.archivo_adj) {
                    html += `<p class="card-info"><a href="${item.archivo_adj}" download>Descargar respaldo</a></p>`;
                }
                
                html += `</div>
                </div>`;
            });
        } else {
            html = '<div class="alert alert-info">No se encontró historial para esta serie.</div>';
        }
        
        document.getElementById('webHistorial').innerHTML = html;
    })
    .catch(error => {
        console.error("Error al recibir la respuesta: ", error);
        document.getElementById('webHistorial').innerHTML = 
            '<div class="alert alert-danger">Error al cargar el historial. Por favor intente nuevamente.</div>';
    });
}

// Función alternativa para cargar historial con datos mock (para desarrollo/testing)
function cargarHistorialMock(serie) {
    // Limpiar el contenido del elemento web antes de enviar la solicitud
    document.getElementById('webHistorial').innerHTML = '<div class="loading-message">Cargando historial...</div>';

    // Simular datos de historial para demostración
    setTimeout(() => {
        const mockData = [
            {
                fecha_hora: '2024-01-15 10:30:00',
                Semantica: 'Asignado a técnico',
                Nombre_origen: 'Bodega Central',
                Nombre_destino: 'Técnico Juan Pérez',
                motivo: 'Asignación inicial',
                observacion: 'Equipo asignado para instalación en cliente'
            },
            {
                fecha_hora: '2024-01-14 14:20:00',
                Semantica: 'Recepción en bodega',
                Nombre_origen: 'Proveedor TechCorp',
                Nombre_destino: 'Bodega Central',
                motivo: 'Recepción de mercadería',
                observacion: 'Equipo recibido en perfectas condiciones'
            },
            {
                fecha_hora: '2024-01-10 09:15:00',
                Semantica: 'Orden de compra',
                Nombre_origen: 'Sistema',
                Nombre_destino: 'Proveedor TechCorp',
                motivo: 'Solicitud de material',
                observacion: 'Orden generada automáticamente'
            }
        ];

        // Procesar la data y renderizar los elementos
        let html = '';
        mockData.forEach(item => {
            html += `<div class="card-item">
                <div class="card-content">
                    <span class="card-date">${item.fecha_hora}</span>
                    <p class="card-info">Movimiento: ${item.Semantica}</p>
                    <p class="card-info">Desde: ${item.Nombre_origen || 'N/A'}</p>`;

            if (item.movimiento_historial) {
                html += `<p class="card-info">Hacia: ${item.Nombre_destino || 'N/A'}</p>`;
            } else {
                html += `<p class="card-info">Hacia: ${item.Nombre_destino || 'N/A'}</p>`;
            }

            if (item.motivo) {
                html += `<p class="card-info">Motivo: ${item.motivo}</p>`;
            }

            if (item.observacion) {
                let textoCorto = item.observacion.substring(0, 30);
                let textoMostrar = (item.observacion.length > 30) ? textoCorto + '...' : textoCorto;
                html += `<p class="card-info" title="${item.observacion}">Observación: ${textoMostrar}</p>`;
            }

            if (item.archivo_adj) {
                html += `<p class="card-info"><a href="${item.archivo_adj}" download>Descargar respaldo</a></p>`;
            }

            html += `   </div>
            </div>`;
        });

        document.getElementById('webHistorial').innerHTML = html;
    }, 500); // Simular delay de carga
}