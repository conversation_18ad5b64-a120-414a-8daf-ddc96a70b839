/**
 * Sistema de carga diferida básico para TQW
 * Versión: 1.0
 * 
 * Este script es un placeholder para el sistema original de lazy loading.
 * Proporciona compatibilidad con código que espera que exista este archivo.
 */

// Log para informar que se está usando la versión simplificada
console.log('🔄 Cargando sistema de lazy loading (compatibilidad)');

// Función para cargar secciones
function loadSectionContent(sectionId) {
    console.log('📦 Cargando sección:', sectionId);
    
    const container = document.getElementById(sectionId + '-container');
    if (!container) {
        console.error('❌ Contenedor no encontrado:', sectionId + '-container');
        return Promise.reject('Contenedor no encontrado');
    }
    
    // Comprobar si la sección ya está cargada
    if (container.classList.contains('loaded')) {
        console.log('✅ La sección ya está cargada:', sectionId);
        return Promise.resolve('La sección ya está cargada');
    }
    
    // URL para cargar la sección
    const mapping = {
        'analytics': 'get_analytics_section.php',
        'dash_logis': 'get_dash_logis.php',
        'desafio_tec_section': 'get_desafio_tec_section.php',
        'cubos': 'get_cubos.php',
        'calidad_reac_section': 'get_calidad_reac_section.php',
        'inventario': 'get_inventario.php',
        'Home': 'get_Home.php',
        'reporte_produccion': 'get_reporte_produccion.php',
        'solicitudMaterial': 'get_solicitudes.php',
        'nicolas_personal': 'get_nicolas_personal.php'
    };
    
    // Construir URL
    let url = mapping[sectionId] || 'get_' + sectionId + '.php';
    
    // Agregar parámetros de sesión
    const urlParams = new URLSearchParams(window.location.search);
    const sessionId = urlParams.get('id_sesion');
    if (sessionId) {
        url += (url.includes('?') ? '&' : '?') + 'id_sesion=' + encodeURIComponent(sessionId);
    }
    
    // Agregar timestamp para evitar caché
    url += (url.includes('?') ? '&' : '?') + '_t=' + Date.now();
    
    console.log('🌐 URL:', url);
    
    // Cargar la sección
    return fetch(url)
        .then(response => {
            console.log('📡 Respuesta recibida:', response.status);
            if (!response.ok) {
                throw new Error(`Error ${response.status}: ${response.statusText}`);
            }
            return response.text();
        })
        .then(html => {
            console.log('📄 HTML recibido (longitud):', html.length);
            
            // Actualizar contenido
            const contentDiv = container.querySelector('.section-content');
            if (contentDiv) {
                contentDiv.innerHTML = html;
            } else {
                const newContentDiv = document.createElement('div');
                newContentDiv.className = 'section-content';
                newContentDiv.innerHTML = html;
                container.appendChild(newContentDiv);
            }
            
            // Marcar como cargada
            container.classList.add('loaded');
            container.classList.remove('loading');
            
            // Disparar evento
            dispatchSectionLoadedEvent(sectionId);
            
            return 'Sección cargada correctamente';
        })
        .catch(error => {
            console.error('❌ Error cargando sección:', error);
            container.classList.add('error');
            container.classList.remove('loading');
            
            // Mostrar mensaje de error
            const errorMessage = document.createElement('div');
            errorMessage.className = 'alert alert-danger m-3';
            errorMessage.innerHTML = `
                <i class="bi bi-exclamation-triangle"></i>
                Error al cargar la sección: ${error.message}
                <button class="btn btn-sm btn-outline-danger mt-2" onclick="LazyLoader.reloadSection('${sectionId}')">
                    <i class="bi bi-arrow-clockwise"></i> Reintentar
                </button>
            `;
            
            const contentDiv = container.querySelector('.section-content');
            if (contentDiv) {
                contentDiv.innerHTML = errorMessage.outerHTML;
            } else {
                container.innerHTML = errorMessage.outerHTML;
            }
            
            throw error;
        });
}

// Función para disparar evento de sección cargada
function dispatchSectionLoadedEvent(sectionId) {
    console.log('🔔 Disparando evento sectionLoaded para:', sectionId);
    
    // Crear evento
    const event = new CustomEvent('sectionLoaded', {
        detail: { sectionId },
        bubbles: true,
        cancelable: true
    });
    
    // Disparar en el documento
    document.dispatchEvent(event);
    
    // También en window para compatibilidad
    window.dispatchEvent(new CustomEvent('sectionContentUpdated', {
        detail: { sectionId }
    }));
}

// Función para recargar una sección
function reloadSection(sectionId) {
    console.log('🔄 Recargando sección:', sectionId);
    
    const container = document.getElementById(sectionId + '-container');
    if (container) {
        container.classList.remove('loaded', 'error');
        loadSectionContent(sectionId);
    }
}

// Exportar API
window.LazyLoader = {
    loadSection: loadSectionContent,
    reloadSection: reloadSection,
    preloadSections: function(sections) {
        if (Array.isArray(sections)) {
            sections.forEach(sectionId => {
                console.log('📥 Precargando sección:', sectionId);
                setTimeout(() => loadSectionContent(sectionId), 1000);
            });
        }
    }
};

// Exponer función para compatibilidad
window.dispatchSectionLoadedEvent = dispatchSectionLoadedEvent;

console.log('✅ Sistema de lazy loading inicializado (compatibilidad)');