/* Estilos para el modal de cierre de sesión */
.logout-modal {
    display: none; /* Oculto por defecto */
    position: fixed;
    z-index: 1055; /* Encima de otros elementos */
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0, 0, 0, 0.5);
    -webkit-animation-name: fadeIn;
    animation-name: fadeIn;
    -webkit-animation-duration: 0.4s;
    animation-duration: 0.4s;
}

.logout-modal-content {
    position: fixed;
    bottom: 0;
    background-color: #2c2c2c; /* Fondo oscuro */
    color: #fff; /* Texto blanco */
    width: 100%;
    border-top-left-radius: 20px;
    border-top-right-radius: 20px;
    box-shadow: 0 -4px 12px rgba(0, 0, 0, 0.3);
    -webkit-animation-name: slideIn;
    animation-name: slideIn;
    -webkit-animation-duration: 0.4s;
    animation-duration: 0.4s;
    padding: 20px;
}

.logout-modal-header {
    padding-bottom: 10px;
    border-bottom: 1px solid #444; /* Borde sutil */
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logout-modal-header h2 {
    margin: 0;
    font-size: 1.5rem;
    font-weight: 500;
}

.logout-modal-body {
    padding: 20px 0;
    font-size: 1.1rem;
}

.logout-modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    padding-top: 10px;
}

.btn-logout-confirm, .btn-logout-cancel {
    padding: 12px 25px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-size: 1rem;
    font-weight: 600;
    transition: background-color 0.3s, transform 0.2s;
}

.btn-logout-confirm {
    background-color: #d9534f; /* Rojo para confirmar */
    color: white;
}

.btn-logout-confirm:hover {
    background-color: #c9302c;
    transform: scale(1.05);
}

.btn-logout-cancel {
    background-color: #555; /* Gris oscuro para cancelar */
    color: white;
}

.btn-logout-cancel:hover {
    background-color: #444;
    transform: scale(1.05);
}

/* Animaciones */
@-webkit-keyframes slideIn {
    from {bottom: -300px; opacity: 0} 
    to {bottom: 0; opacity: 1}
}

@keyframes slideIn {
    from {bottom: -300px; opacity: 0}
    to {bottom: 0; opacity: 1}
}

@-webkit-keyframes fadeIn {
    from {opacity: 0} 
    to {opacity: 1}
}

@keyframes fadeIn {
    from {opacity: 0} 
    to {opacity: 1}
}
