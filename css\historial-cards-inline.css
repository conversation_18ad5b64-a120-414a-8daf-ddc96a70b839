/**
 * historial-cards-inline.css
 * Estilos UNIFICADOS para las tarjetas de historial en el módulo de logística
 * Versión 5.0 - Estilos consolidados de múltiples archivos para evitar conflictos
 * NOTA: Este archivo contiene TODOS los estilos relacionados con el historial
 */

/* Ajustes para el offcanvas de historial */
#offcanvasHistorial .offcanvas-body,
#canvaHistorial .offcanvas-body {
    background-color: #1e2746;
    padding: 15px !important;
}

/* Ajustes para el contenedor de historial */
#webHistorial {
    padding: 12px !important;
    border-radius: 8px !important;
    border: 1px solid rgba(0, 225, 253, 0.3) !important;
    background-color: rgba(30, 39, 70, 0.95) !important;
    max-height: 350px !important;
    overflow-y: auto !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2) !important;
    color: white !important;
}

/* Scrollbar personalizada */
#webHistorial::-webkit-scrollbar {
    width: 5px !important;
}

#webHistorial::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.05) !important;
}

#webHistorial::-webkit-scrollbar-thumb {
    background: rgba(0, 225, 253, 0.5) !important;
    border-radius: 3px !important;
}

/* Selector específico para las tarjetas - Exactamente como en la captura */
#webHistorial .card-item {
    background: rgb(25, 33, 60) !important;
    border: 1px solid rgba(0, 225, 253, 0.3) !important;
    border-radius: 6px !important;
    padding: 12px !important;
    margin-bottom: 15px !important;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2) !important;
}

/* Selector específico para el contenido de las tarjetas */
#webHistorial .card-content {
    display: flex !important;
    flex-direction: column !important;
    gap: 8px !important;
}

/* Estilos para la fecha/hora en la parte superior - Como en la captura */
#webHistorial .card-date,
#webHistorial span.card-date {
    background-color: #0066b8 !important;
    color: white !important;
    border-radius: 4px !important;
    padding: 4px 10px !important;
    margin-bottom: 8px !important;
    display: inline-flex !important;
    align-items: center !important;
    font-size: 13px !important;
    font-weight: 500 !important;
    max-width: fit-content !important;
}

/* Estilos generales para alinear iconos y texto - Como en la captura */
#webHistorial .card-info,
p.card-info {
    display: flex !important;
    align-items: center !important;
    margin-bottom: 8px !important;
    font-size: 14px !important;
    line-height: 1.5 !important;
    padding: 0 !important;
    margin-left: 0 !important;
    margin-right: 0 !important;
}

/* Ajustes específicos para los iconos - Como en la captura */
#webHistorial i.bi {
    width: 24px !important;
    margin-right: 10px !important;
    text-align: center !important;
    font-size: 16px !important;
    flex-shrink: 0 !important;
}

/* Colores específicos para cada tipo de icono - Adaptados a la captura */
#webHistorial i.bi-calendar,
#webHistorial i[class*="calendar"] {
    color: white !important;
}

#webHistorial i.bi-box, 
#webHistorial i.bi-arrow-left-right, 
#webHistorial i[class*="box"] {
    color: #00b8d4 !important;
}

#webHistorial i.bi-geo-alt, 
#webHistorial i.bi-geo-alt-fill, 
#webHistorial i[class*="geo"] {
    color: #f44336 !important;
}

#webHistorial i.bi-heart, 
#webHistorial i[class*="heart"], 
#webHistorial i[class*="heart-fill"] {
    color: #aa00ff !important;
}

#webHistorial i.bi-chat, 
#webHistorial i.bi-chat-text, 
#webHistorial i.bi-chat-square-text, 
#webHistorial i.bi-chat-dots,
#webHistorial i[class*="chat"] {
    color: #ff9800 !important;
}

#webHistorial i.bi-file, 
#webHistorial i.bi-download,
#webHistorial i.bi-file-earmark-arrow-down,
#webHistorial i[class*="file"], 
#webHistorial i[class*="download"] {
    color: #26a69a !important;
}

/* Estilos para los campos de entrada */
#serieHistorial, #precioHistorial {
    background-color: rgba(0, 0, 0, 0.2) !important;
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
    color: white !important;
}

/* Mejora para los bordes y estilo general del offcanvas */
.offcanvas-header {
    border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
}

/* Estilo específico para los indicadores visuales */
.form-label {
    color: rgba(255, 255, 255, 0.8) !important;
    font-weight: 500 !important;
}

/* Estilo para links dentro del historial */
#webHistorial a {
    color: #4fc3f7 !important;
    text-decoration: none !important;
    transition: color 0.2s !important;
}

#webHistorial a:hover {
    color: #81d4fa !important;
    text-decoration: underline !important;
}

/* Texto para coincidencia exacta con captura - Etiquetas */
#webHistorial p:has(i.bi-box)::before {
    content: "Movimiento: " !important;
    font-weight: 600 !important;
    margin-right: 4px !important;
}

#webHistorial p:has(i.bi-geo-alt)::before {
    content: "Desde: " !important;
    font-weight: 600 !important;
    margin-right: 4px !important;
}

#webHistorial p:has(i.bi-heart)::before {
    content: "Hacia: " !important;
    font-weight: 600 !important;
    margin-right: 4px !important;
}

/* Asegurar que el primer div (fecha) sea el único con ese background */
#webHistorial > div > div:first-of-type {
    background-color: #0066b8 !important;
    display: inline-flex !important;
    align-items: center !important;
    border-radius: 4px !important;
    padding: 4px 10px !important;
    margin-bottom: 8px !important;
    font-size: 13px !important;
    font-weight: 500 !important;
}

/* Estilos específicos para elementos generados por el script historial_loader.js */
#webHistorial .movimiento-info,
#webHistorial .desde-info,
#webHistorial .hacia-info,
#webHistorial .motivo-info,
#webHistorial .obs-info,
#webHistorial .archivo-info {
    display: flex !important;
    align-items: center !important;
    margin-bottom: 8px !important;
    font-size: 14px !important;
}

#webHistorial .movimiento-info i,
#webHistorial .desde-info i,
#webHistorial .hacia-info i,
#webHistorial .motivo-info i,
#webHistorial .obs-info i,
#webHistorial .archivo-info i {
    width: 24px !important;
    margin-right: 10px !important;
    text-align: center !important;
    font-size: 16px !important;
    flex-shrink: 0 !important;
}

/* ========================================
   ESTILOS ADICIONALES MIGRADOS DESDE mod_logistica.css
   ======================================== */

/* Scrollbar mejorado para el contenedor de historial */
#webHistorial::-webkit-scrollbar {
    width: 6px !important;
}

#webHistorial::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1) !important;
    border-radius: 3px !important;
}

#webHistorial::-webkit-scrollbar-thumb {
    background: #00e1fd !important;
    border-radius: 3px !important;
}

#webHistorial::-webkit-scrollbar-thumb:hover {
    background: #00c4e6 !important;
}

/* Mejoras para elementos de fecha con iconos */
#webHistorial .historial-date i {
    font-size: 16px !important;
    opacity: 0.8 !important;
}

/* Tipo de movimiento con diseño mejorado */
#webHistorial .historial-movement-type {
    background: linear-gradient(135deg, rgba(0, 225, 253, 0.2) 0%, rgba(40, 167, 69, 0.2) 100%) !important;
    color: white !important;
    padding: 4px 12px !important;
    border-radius: 20px !important;
    font-size: 12px !important;
    font-weight: 600 !important;
    text-transform: uppercase !important;
    letter-spacing: 0.5px !important;
    border: 1px solid rgba(0, 225, 253, 0.3) !important;
}

/* Flujo de ubicaciones mejorado */
#webHistorial .historial-location-flow {
    display: flex !important;
    align-items: center !important;
    gap: 12px !important;
    margin-bottom: 12px !important;
    padding: 12px !important;
    background: rgba(0, 0, 0, 0.2) !important;
    border-radius: 8px !important;
    border-left: 3px solid #00e1fd !important;
}

#webHistorial .location-item {
    display: flex !important;
    align-items: center !important;
    gap: 6px !important;
    flex: 1 !important;
}

#webHistorial .location-item i {
    font-size: 14px !important;
    opacity: 0.9 !important;
}

#webHistorial .location-label {
    font-size: 12px !important;
    color: rgba(255, 255, 255, 0.7) !important;
    font-weight: 500 !important;
    text-transform: uppercase !important;
    letter-spacing: 0.5px !important;
}

#webHistorial .location-value {
    color: white !important;
    font-weight: 600 !important;
    font-size: 13px !important;
}

#webHistorial .location-arrow {
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    padding: 0 8px !important;
}

#webHistorial .location-arrow i {
    font-size: 16px !important;
    color: #00e1fd !important;
    animation: pulse 2s infinite !important;
}

/* Detalles adicionales del historial */
#webHistorial .historial-detail {
    display: flex !important;
    align-items: flex-start !important;
    gap: 8px !important;
    margin-bottom: 8px !important;
    padding: 8px 12px !important;
    background: rgba(255, 255, 255, 0.03) !important;
    border-radius: 6px !important;
    border-left: 2px solid rgba(0, 225, 253, 0.4) !important;
}

#webHistorial .historial-detail i {
    font-size: 14px !important;
    color: #00e1fd !important;
    margin-top: 2px !important;
    opacity: 0.8 !important;
}

#webHistorial .detail-label {
    font-size: 12px !important;
    color: rgba(255, 255, 255, 0.7) !important;
    font-weight: 500 !important;
    text-transform: uppercase !important;
    letter-spacing: 0.5px !important;
    min-width: 80px !important;
}

#webHistorial .detail-value {
    color: white !important;
    font-size: 13px !important;
    line-height: 1.4 !important;
    flex: 1 !important;
}

/* Pie de la tarjeta de historial */
#webHistorial .historial-footer {
    padding-top: 8px !important;
    border-top: 1px solid rgba(255, 255, 255, 0.1) !important;
}

/* Botones de descarga mejorados */
#webHistorial .historial-download-btn {
    display: inline-flex !important;
    align-items: center !important;
    gap: 8px !important;
    padding: 8px 16px !important;
    background: linear-gradient(135deg, rgba(0, 225, 253, 0.1) 0%, rgba(40, 167, 69, 0.1) 100%) !important;
    border: 1px solid rgba(0, 225, 253, 0.3) !important;
    border-radius: 20px !important;
    color: #00e1fd !important;
    text-decoration: none !important;
    font-size: 12px !important;
    font-weight: 600 !important;
    transition: all 0.3s ease !important;
    text-transform: uppercase !important;
    letter-spacing: 0.5px !important;
}

#webHistorial .historial-download-btn:hover {
    background: linear-gradient(135deg, rgba(0, 225, 253, 0.2) 0%, rgba(40, 167, 69, 0.2) 100%) !important;
    border-color: #00e1fd !important;
    color: white !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 8px rgba(0, 225, 253, 0.2) !important;
}

#webHistorial .historial-download-btn i {
    font-size: 14px !important;
}

/* Mensajes de estado cuando no hay historial */
#webHistorial .alert-warning {
    background: rgba(255, 193, 7, 0.1) !important;
    border: 1px solid rgba(255, 193, 7, 0.3) !important;
    color: #ffd43b !important;
    border-radius: 8px !important;
    padding: 12px !important;
    text-align: center !important;
}

#webHistorial .alert-danger {
    background: rgba(220, 53, 69, 0.1) !important;
    border: 1px solid rgba(220, 53, 69, 0.3) !important;
    color: #ff6b6b !important;
    border-radius: 8px !important;
    padding: 12px !important;
    text-align: center !important;
}

/* Mensaje de carga */
#webHistorial .loading-message {
    text-align: center !important;
    color: #00e1fd !important;
    font-style: italic !important;
    padding: 20px !important;
}

/* Animación de pulso para elementos dinámicos */
@keyframes pulse {
    0% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
    100% {
        opacity: 1;
    }
}

/* Mejoras para el header del offcanvas */
#offcanvasHistorial .offcanvas-header {
    background: linear-gradient(135deg, #0077b6 0%, #023e8a 100%) !important;
    color: white !important;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
}

#offcanvasHistorial .offcanvas-title {
    color: white !important;
    font-weight: 600 !important;
}

#offcanvasHistorial .btn-close {
    filter: invert(1) !important;
}

/* Mejoras para los campos de formulario en el offcanvas */
#offcanvasHistorial .form-label {
    color: rgba(255, 255, 255, 0.8) !important;
    font-weight: 500 !important;
    font-size: 14px !important;
    margin-bottom: 5px !important;
}

#offcanvasHistorial .form-control {
    background: rgba(255, 255, 255, 0.05) !important;
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
    color: white !important;
    border-radius: 6px !important;
    padding: 8px 12px !important;
    font-size: 14px !important;
}

#offcanvasHistorial .form-control:focus {
    background: rgba(255, 255, 255, 0.08) !important;
    border-color: #00e1fd !important;
    box-shadow: 0 0 0 0.2rem rgba(0, 225, 253, 0.25) !important;
    color: white !important;
}