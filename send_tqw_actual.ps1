param([int]$TimeframeHours = 3)

$SERVER = "servidor-tqw"
$PORT = "2277"
$DEST_BASE = "C:/wamp64/www/op_Tqw/APP_TQW/dist"

Write-Host "Iniciando transferencia de archivos modificados en las ultimas $TimeframeHours horas..." -ForegroundColor Green

# Función para verificar conectividad
function Test-ServerConnection {
    Write-Host "Verificando conectividad con $SERVER..." -ForegroundColor Cyan
    $testResult = ssh -p $PORT $SERVER "echo 'Conexion exitosa'"
    if ($LASTEXITCODE -eq 0) {
        Write-Host "Conectividad verificada exitosamente" -ForegroundColor Green
        return $true
    } else {
        Write-Host "Error de conectividad con el servidor" -ForegroundColor Red
        return $false
    }
}

# Verificar conectividad antes de proceder
if (-not (Test-ServerConnection)) {
    Write-Host "No se puede continuar sin conectividad al servidor" -ForegroundColor Red
    exit 1
}

$timeLimit = (Get-Date).AddHours(-$TimeframeHours)

$directoriesToCheck = @(
    @{ Path = "."; Filter = "*.php"; Dest = "$DEST_BASE" },
    @{ Path = "."; Filter = "*.html"; Dest = "$DEST_BASE" },
    @{ Path = "js"; Filter = "*.*"; Dest = "$DEST_BASE\js" },
    @{ Path = "css"; Filter = "*.*"; Dest = "$DEST_BASE\css" },
    @{ Path = "components"; Filter = "*.*"; Dest = "$DEST_BASE\components" },
    @{ Path = "includes"; Filter = "*.*"; Dest = "$DEST_BASE\includes" },
    @{ Path = "js\modules"; Filter = "*.*"; Dest = "$DEST_BASE\js\modules" },
    @{ Path = "js\modules\calidadReactiva"; Filter = "*.*"; Dest = "$DEST_BASE\js\modules\calidadReactiva" },
    @{ Path = "js\mod_logistica"; Filter = "*.*"; Dest = "$DEST_BASE\js\mod_logistica" },
    @{ Path = "config"; Filter = "*.*"; Dest = "$DEST_BASE\config" }
)

foreach ($dir in $directoriesToCheck) {
    if (Test-Path $dir.Path) {
        $recentFiles = Get-ChildItem -Path $dir.Path -Filter $dir.Filter -File | Where-Object { $_.LastWriteTime -gt $timeLimit }

        if ($recentFiles.Count -gt 0) {
            Write-Host "Transfiriendo archivos de $($dir.Path)..." -ForegroundColor Yellow

            # Crear directorio remoto si no existe
            Write-Host "  Creando directorio remoto..." -ForegroundColor Magenta

            # Crear directorio usando cmd en el servidor Windows
            $createCmd = "if not exist `"$($dir.Dest)`" mkdir `"$($dir.Dest)`""
            ssh -p $PORT $SERVER "cmd /c `"$createCmd`""

            if ($LASTEXITCODE -eq 0) {
                Write-Host "  Directorio verificado/creado exitosamente" -ForegroundColor Green
            } else {
                Write-Host "  Advertencia: No se pudo verificar/crear el directorio" -ForegroundColor Yellow
            }

            foreach ($file in $recentFiles) {
                Write-Host "  Transfiriendo $($file.Name)..." -ForegroundColor Cyan

                # Convertir ruta para SCP (usar forward slashes)
                $scpDest = $dir.Dest -replace "\\", "/"
                scp -P $PORT $file.FullName "${SERVER}:$scpDest/"

                if ($LASTEXITCODE -eq 0) {
                    Write-Host "  OK $($file.Name)" -ForegroundColor Green
                } else {
                    Write-Host "  ERROR $($file.Name)" -ForegroundColor Red
                    Write-Host "    Ruta destino: ${SERVER}:$scpDest/" -ForegroundColor Red
                }
            }
        }
    } else {
        Write-Host "Directorio local no encontrado: $($dir.Path)" -ForegroundColor Yellow
    }
}

Write-Host "Transferencia completada!" -ForegroundColor Green