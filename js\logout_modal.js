document.addEventListener('DOMContentLoaded', function () {
    const logoutButton = document.getElementById('logoutButton');
    const logoutModal = document.getElementById('logoutModal');
    const cancelLogout = document.getElementById('cancelLogout');
    const confirmLogout = document.getElementById('confirmLogout');

    if (logoutButton) {
        logoutButton.addEventListener('click', function (e) {
            e.preventDefault(); // Evita la navegación directa
            logoutModal.style.display = 'block';
        });
    }

    if (cancelLogout) {
        cancelLogout.addEventListener('click', function () {
            logoutModal.style.display = 'none';
        });
    }

    // Cierra el modal si se hace clic fuera del contenido
    window.addEventListener('click', function (event) {
        if (event.target == logoutModal) {
            logoutModal.style.display = 'none';
        }
    });
});
