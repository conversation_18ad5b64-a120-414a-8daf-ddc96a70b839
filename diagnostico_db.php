<?php
/**
 * SECURE Database Diagnostic Script
 *
 * SECURITY NOTICE: This script is for AUTHORIZED ADMINISTRATORS ONLY
 *
 * SECURITY FEATURES:
 * - IP-based access control
 * - Authentication required
 * - No credential exposure
 * - Secure logging
 * - Rate limiting
 *
 * @version 2.0.0 - Security Enhanced
 */

// CRITICAL SECURITY: Restrict access to authorized IPs only
$authorizedIPs = [
    '127.0.0.1',
    '::1',
    'localhost'
    // Add your admin IP addresses here
];

$clientIP = $_SERVER['REMOTE_ADDR'] ?? '';
$isAuthorized = in_array($clientIP, $authorizedIPs);

// Additional authorization check - require admin session
session_start();
$isAdminSession = isset($_SESSION['admin_authenticated']) && $_SESSION['admin_authenticated'] === true;

// If not authorized, log attempt and deny access
if (!$isAuthorized && !$isAdminSession) {
    error_log("SECURITY ALERT: Unauthorized access attempt to diagnostic script from IP: $clientIP");
    http_response_code(403);
    exit('Access Denied');
}

// Include secure configuration
define('APP_ACCESS', true);
require_once __DIR__ . '/config/database.php';

// Get secure configuration
$config = getDatabaseConfig();
$securityConfig = getSecurityConfig();

// Log diagnostic access
logSecureError('Database diagnostic accessed', [
    'client_ip' => $clientIP,
    'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
    'authorized' => $isAuthorized ? 'yes' : 'no'
], 'INFO');

?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Diagnóstico Seguro - Base de Datos</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .diagnostic-card { margin-bottom: 1rem; }
        .status-success { color: #28a745; }
        .status-error { color: #dc3545; }
        .status-warning { color: #ffc107; }
        .secure-info { background-color: #f8f9fa; padding: 1rem; border-radius: 0.5rem; }
    </style>
</head>
<body class="bg-light">
    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h4 class="mb-0">
                            <i class="bi bi-shield-check me-2"></i>
                            Diagnóstico Seguro de Base de Datos
                        </h4>
                        <small>Ejecutado el <?php echo date('Y-m-d H:i:s'); ?> | Entorno: <?php echo $securityConfig['environment']; ?></small>
                    </div>
                    <div class="card-body">

                        <?php
                        // 1. Test database connection securely
                        echo '<div class="diagnostic-card">';
                        echo '<h5><i class="bi bi-database me-2"></i>1. Estado de Conexión a Base de Datos</h5>';

                        try {
                            require_once 'DatabaseConnection.php';
                            $db = DatabaseConnection::getInstance();
                            $connection = $db->getConnection();

                            if ($connection) {
                                echo '<div class="alert alert-success">';
                                echo '<i class="bi bi-check-circle-fill me-2"></i>';
                                echo '<strong>Conexión Exitosa:</strong> Base de datos accesible';
                                echo '</div>';

                                // Test basic query
                                $result = $connection->query("SELECT 1 as test");
                                if ($result) {
                                    echo '<div class="alert alert-info">';
                                    echo '<i class="bi bi-check-circle me-2"></i>';
                                    echo '<strong>Consulta de Prueba:</strong> Exitosa';
                                    echo '</div>';
                                    $result->free();
                                }

                                // Get connection stats (safely)
                                $stats = $db->getConnectionStats();
                                if ($stats) {
                                    echo '<div class="secure-info">';
                                    echo '<h6>Estadísticas de Conexión:</h6>';
                                    echo '<ul class="mb-0">';
                                    foreach ($stats as $key => $value) {
                                        echo "<li><strong>" . htmlspecialchars($key) . ":</strong> " . htmlspecialchars($value) . "</li>";
                                    }
                                    echo '</ul>';
                                    echo '</div>';
                                }

                            } else {
                                echo '<div class="alert alert-danger">';
                                echo '<i class="bi bi-x-circle-fill me-2"></i>';
                                echo '<strong>Error:</strong> No se pudo establecer conexión';
                                echo '</div>';
                            }

                        } catch (DatabaseConnectionException $e) {
                            echo '<div class="alert alert-warning">';
                            echo '<i class="bi bi-exclamation-triangle-fill me-2"></i>';
                            echo '<strong>Error de Conexión:</strong> ' . htmlspecialchars($e->getMessage());
                            echo '</div>';

                        } catch (Exception $e) {
                            echo '<div class="alert alert-danger">';
                            echo '<i class="bi bi-x-circle-fill me-2"></i>';
                            echo '<strong>Error Inesperado:</strong> Error interno del sistema';
                            echo '</div>';

                            // Log detailed error for admin review
                            logSecureError('Diagnostic script error', [
                                'error_class' => get_class($e),
                                'error_message' => $e->getMessage(),
                                'file' => $e->getFile(),
                                'line' => $e->getLine()
                            ], 'ERROR');
                        }

                        echo '</div>';
                        ?>

// 2. Verificar configuración de red
echo "<h3>3. Configuración de Red</h3>";
echo "🌐 <strong>Host configurado:</strong> " . $config['host'] . "<br>";
echo "👤 <strong>Usuario:</strong> " . $config['user'] . "<br>";
echo "🗄️ <strong>Base de datos:</strong> " . $config['database'] . "<br>";

// 3. Verificar puertos
echo "<h3>4. Verificación de Puertos</h3>";
$mysql_port = 3306;
$connection = @fsockopen($config['host'], $mysql_port, $errno, $errstr, 5);
if ($connection) {
    echo "✅ <strong>Puerto MySQL ($mysql_port):</strong> ABIERTO<br>";
    fclose($connection);
} else {
    echo "❌ <strong>Puerto MySQL ($mysql_port):</strong> CERRADO o INACCESIBLE<br>";
    echo "📋 <strong>Error:</strong> $errstr ($errno)<br>";
}

// 4. Verificar variables del sistema
echo "<h3>5. Variables del Sistema</h3>";
echo "🖥️ <strong>Sistema operativo:</strong> " . PHP_OS . "<br>";
echo "🐘 <strong>Versión PHP:</strong> " . PHP_VERSION . "<br>";
echo "📦 <strong>Extensión MySQLi:</strong> " . (extension_loaded('mysqli') ? '✅ CARGADA' : '❌ NO CARGADA') . "<br>";

// 5. Verificar archivos de configuración
echo "<h3>6. Archivos de Configuración</h3>";
$config_files = [
    'DatabaseConnection.php' => file_exists('DatabaseConnection.php'),
    'login.php' => file_exists('login.php'),
];

foreach ($config_files as $file => $exists) {
    echo "📄 <strong>$file:</strong> " . ($exists ? '✅ EXISTE' : '❌ NO EXISTE') . "<br>";
}

// 6. Verificar procesos MySQL (solo en Windows con WAMP)
echo "<h3>7. Procesos del Sistema</h3>";
if (strtoupper(substr(PHP_OS, 0, 3)) === 'WIN') {
    echo "🪟 <strong>Sistema Windows detectado</strong><br>";
    
    // Verificar si WAMP está ejecutándose
    $wamp_processes = [
        'mysqld.exe',
        'httpd.exe',
        'wampmanager.exe'
    ];
    
    foreach ($wamp_processes as $process) {
        $output = [];
        $return_var = 0;
        exec("tasklist /FI \"IMAGENAME eq $process\" 2>NUL", $output, $return_var);
        
        $running = false;
        foreach ($output as $line) {
            if (stripos($line, $process) !== false) {
                $running = true;
                break;
            }
        }
        
        echo "⚙️ <strong>$process:</strong> " . ($running ? '✅ EJECUTÁNDOSE' : '❌ NO EJECUTÁNDOSE') . "<br>";
    }
} else {
    echo "🐧 <strong>Sistema Unix/Linux detectado</strong><br>";
    echo "ℹ️ Para verificar procesos MySQL, ejecute: <code>ps aux | grep mysql</code><br>";
}

// 7. Recomendaciones
echo "<h3>8. Recomendaciones</h3>";
echo "<div style='background-color: #f0f8ff; padding: 10px; border-left: 4px solid #0066cc;'>";
echo "<strong>Si hay errores de conexión:</strong><br>";
echo "1. ✅ Verificar que WAMP/XAMPP esté ejecutándose<br>";
echo "2. ✅ Reiniciar el servicio MySQL<br>";
echo "3. ✅ Verificar credenciales en DatabaseConnection.php<br>";
echo "4. ✅ Comprobar que el puerto 3306 no esté bloqueado<br>";
echo "5. ✅ Revisar logs de MySQL en: <code>C:\\wamp64\\logs\\mysql.log</code><br>";
echo "</div>";

// 8. Test de conexión completa
echo "<h3>9. Test de Conexión Completa</h3>";
try {
    require_once 'DatabaseConnection.php';
    $db = DatabaseConnection::getInstance();
    $connection = $db->getConnection();
    
    if ($connection) {
        echo "✅ <strong>DatabaseConnection::getInstance():</strong> EXITOSO<br>";
        
        // Test de consulta simple
        $result = $connection->query("SELECT 1 as test");
        if ($result) {
            echo "✅ <strong>Consulta de prueba:</strong> EXITOSA<br>";
            $result->free();
        } else {
            echo "❌ <strong>Consulta de prueba:</strong> FALLIDA<br>";
        }
        
        // Verificar estadísticas de conexión
        $stats = $db->getConnectionStats();
        if ($stats) {
            echo "📊 <strong>Estadísticas de conexión:</strong><br>";
            foreach ($stats as $key => $value) {
                echo "&nbsp;&nbsp;&nbsp;&nbsp;• $key: $value<br>";
            }
        }
        
    } else {
        echo "❌ <strong>DatabaseConnection::getInstance():</strong> FALLIDO<br>";
    }
} catch (Exception $e) {
    echo "❌ <strong>Error en test completo:</strong> " . $e->getMessage() . "<br>";
}

echo "<hr>";
echo "<p><em>Diagnóstico completado a las " . date('Y-m-d H:i:s') . "</em></p>";
?>
