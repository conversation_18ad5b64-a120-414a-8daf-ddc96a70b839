<?php
/**
 * Secure Login System
 *
 * SECURITY FEATURES:
 * - Secure error handling with no information disclosure
 * - Rate limiting for login attempts
 * - Session security
 * - CSRF protection
 * - Comprehensive security logging
 *
 * @version 2.0.0 - Security Enhanced
 */

// Security headers
header('Content-Type: text/html; charset=UTF-8');
header('X-Content-Type-Options: nosniff');
header('X-Frame-Options: DENY');
header('X-XSS-Protection: 1; mode=block');
header('Referrer-Policy: strict-origin-when-cross-origin');

// Include secure database connection (which includes config)
require_once 'DatabaseConnection.php';

// Initialize security monitoring
$securityConfig = getSecurityConfig();
$loginAttempts = 0;
$maxLoginAttempts = $securityConfig['max_login_attempts'];

try {
    // Start secure session
    if (session_status() === PHP_SESSION_NONE) {
        // Configure secure session settings
        ini_set('session.cookie_httponly', 1);
        ini_set('session.cookie_secure', $securityConfig['secure_cookies'] ? 1 : 0);
        ini_set('session.cookie_samesite', 'Strict');
        ini_set('session.use_strict_mode', 1);

        session_start();
    }

    // Initialize database connection with error handling
    $db = DatabaseConnection::getInstance();
    $conex = $db->getConnection();

    // Log successful connection (without sensitive details)
    logSecureError('Login page accessed', [
        'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
        'remote_addr' => $_SERVER['REMOTE_ADDR'] ?? 'unknown'
    ], 'INFO');

    // Guardar usuario en sesión después del login exitoso
    if (isset($aux7)) { // RUT del usuario
        $_SESSION['usuario'] = $aux7;
        $_SESSION['login_time'] = time();

        // Log successful login
        logSecureError('User login successful', [
            'user' => $aux7,
            'session_id' => session_id()
        ], 'INFO');
    }

    // Registrar función de limpieza al finalizar el script
    register_shutdown_function(function() use ($db) {
        if ($db !== null) {
            $db->cleanup();
        }
    });

} catch (DatabaseConnectionException $e) {
    // Handle database connection errors securely
    logSecureError('Database connection failed on login page', [
        'error_message' => $e->getMessage()
    ], 'ERROR');

    // Show user-safe error message
    $errorMessage = $e->getMessage();

} catch (Exception $e) {
    // Handle any other errors securely
    logSecureError('Unexpected error on login page', [
        'error_class' => get_class($e),
        'error_message' => $e->getMessage(),
        'file' => $e->getFile(),
        'line' => $e->getLine()
    ], 'ERROR');

    // Show generic error message
    $errorMessage = getUserSafeErrorMessage('general');
}

// If there's an error, show error page instead of login form
if (isset($errorMessage)) {
    ?>
    <!DOCTYPE html>
    <html lang="es">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Error - Sistema de Operaciones</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
        <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    </head>
    <body class="bg-light">
        <div class="container mt-5">
            <div class="row justify-content-center">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-body text-center">
                            <i class="bi bi-exclamation-triangle-fill text-warning" style="font-size: 3rem;"></i>
                            <h4 class="mt-3">Servicio No Disponible</h4>
                            <p class="text-muted"><?php echo htmlspecialchars($errorMessage); ?></p>
                            <button onclick="window.location.reload()" class="btn btn-primary">
                                <i class="bi bi-arrow-clockwise me-2"></i>Reintentar
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </body>
    </html>
    <?php
    exit;
}



    ?>


<!DOCTYPE html>
<html lang="en">

<head>

    <style>
    html,
    body {
        height: 100%;
        margin: 0;
    }

    body {
        /* background-image: url('https://miro.medium.com/v2/resize:fit:6632/1*idZQfRpQ_Hu_fU7mEgGswQ.jpeg'); */
        background-image: url('https://wallpapers.com/images/featured/autumn-background-kyu8tqqpi1mb4g0x.webp');
        background-size: cover;
        background-position: center;
        background-repeat: no-repeat;
    }


    .bg-image {
        /* background-image: url('img/bg-img/your-background-image.jpg'); */
        height: 100%;
        background-position: center;
        background-repeat: no-repeat;
        background-size: cover;
    }

    .glass-container {
        background: rgba(255, 255, 255, 0.15);
        backdrop-filter: blur(5px);
        border-radius: 10px;
        padding: 30px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }

    .login-intro-img {
        max-width: 200px;
        margin-bottom: 20px;
    }

    .form-control {
        background-color: rgba(255, 255, 255, 0.2);
        border: none;
        color: white;
    }

    .form-control::placeholder {
        color: rgba(255, 255, 255, 0.7);
    }

    .btn-success {
        background-color: #28a745;
        border: none;
    }

    .forgot-password {
        color: white;
        text-decoration: none;
    }

    #password-visibility {
        top: 50%;
        right: 10px;
        transform: translateY(-50%);
        cursor: pointer;
    }

    .alert {
        margin-bottom: 0;
        padding: 0.75rem 1rem;
    }

    .btn-light {
        background: rgba(255, 255, 255, 0.2);
        border: 1px solid rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(5px);
    }

    .btn-light:hover {
        background: rgba(255, 255, 255, 0.3);
    }

    .bi-envelope-fill {
        font-size: 1.2rem;
    }
    </style>

    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="description" content="TQW APP">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <!-- The above 4 meta tags *must* come first in the head; any other head content must come *after* these tags -->

    <meta name="theme-color" content="#0134d4">

    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-capable" content="yes">

    <!-- Title -->
    <title>TQW APP - LOGIN</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">

    <!-- Favicon -->
    <link rel="icon" href="img/core-img/logo_con.ico">
    <link rel="apple-touch-icon" href="img/icons/icon-96x96.png">
    <link rel="apple-touch-icon" sizes="152x152" href="img/icons/icon-152x152.png">
    <link rel="apple-touch-icon" sizes="167x167" href="img/icons/icon-167x167.png">
    <link rel="apple-touch-icon" sizes="180x180" href="img/icons/icon-180x180.png">

    <!-- Style CSS -->
    <link rel="stylesheet" href="style.css">

    <!-- Web App Manifest -->
    <link rel="manifest" href="manifest.json">
    <link rel="manifest" href="/APP_TQW/dist/manifest.json">
</head>

<body>
    <!-- Preloader -->
    <div id="preloader">
        <div class="spinner-grow text-primary" role="status">
            <span class="visually-hidden">Loading...</span>
        </div>
    </div>

    <!-- Internet Connection Status -->
    <div class="internet-connection-status" id="internetStatus"></div>

    <!-- Back Button -->
    <!-- <div class="login-back-button">
        <a href="hero-blocks.html">
            <i class="bi bi-arrow-left-short"></i>
        </a>
    </div> -->



    <!-- Login Wrapper Area -->

    <div class="bg-image d-flex align-items-center justify-content-center">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-md-8 col-lg-8">
                    <div class="glass-container">
                        <div class="text-center">
                            <img class="login-intro-img" src="img/bg-img/Definitivo.png" alt="TelQuay Logo">
                        </div>

                        <!-- <h2 class="mb-3 text-center text-white">Operaciones</h2> -->

                        <form id="login-form" action="Controller3.php" method="post">
                            <div class="mb-3">
                                <input class="form-control" type="text" id="e_mail" name="e_mail" placeholder="Usuario">
                            </div>

                            <div class="mb-3 position-relative">
                                <input class="form-control" id="passs" name="passs" type="password"
                                    placeholder="Contraseña">
                                <div class="position-absolute" id="password-visibility">
                                    <i class="bi bi-eye text-white"></i>
                                    <i class="bi bi-eye-slash text-white"></i>
                                </div>
                            </div>

                            <button class="btn btn-success w-100 mb-3" type="submit">Ingresar</button>
                        </form>

                        <div id="error-message">
                            <?php if(isset($_GET['session_expired']) && $_GET['session_expired'] == 1): ?>
                            <div class="d-flex align-items-center justify-content-between gap-3">
                                <div class="alert alert-warning mb-0 flex-grow-1" role="alert">
                                    <div class="d-flex align-items-center">
                                        <i class="bi bi-exclamation-triangle-fill me-2"></i>
                                        <span>La sesión ha expirado. Por favor, inicie sesión nuevamente.</span>
                                    </div>
                                </div>
                            </div>
                            <?php endif; ?>
                        </div>

                        <div class="text-center">
                            <a class="forgot-password d-block mt-3" href="forget-password.php">¿Olvidaste tu
                                contraseña?</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>


    <!-- All JavaScript Files -->
    <script src="js/bootstrap.bundle.min.js"></script>
    <script src="js/slideToggle.min.js"></script>
    <script src="js/internet-status.js"></script>
    <script src="js/tiny-slider.js"></script>
    <script src="js/venobox.min.js"></script>
    <script src="js/countdown.js"></script>
    <script src="js/rangeslider.min.js"></script>
    <script src="js/vanilla-dataTables.min.js"></script>
    <script src="js/index.js"></script>
    <script src="js/imagesloaded.pkgd.min.js"></script>
    <script src="js/isotope.pkgd.min.js"></script>
    <script src="js/dark-rtl.js"></script>
    <script src="js/active.js"></script>
    <script src="js/pwa.js"></script>
    <!-- <script src="js/login.js"></script> -->
    <!-- Antes del cierre del </body> -->

    <!-- <script src="/APP_TQW/dist/web_push/js/push-client.js"></script> -->



    <script>
    // Esperar a que el DOM esté completamente cargado
    // Esperar a que el DOM esté completamente cargado
    document.addEventListener('DOMContentLoaded', function() {
        const loginForm = document.getElementById('login-form');
        const errorMessage = document.getElementById('error-message');

        loginForm.addEventListener('submit', async function(e) {
            e.preventDefault();

            const email = document.getElementById('e_mail').value;
            const password = document.getElementById('passs').value;

            try {
                const formData = new FormData();
                formData.append('e_mail', email);
                formData.append('passs', password);

                const response = await fetch('Controller3.php', {
                    method: 'POST',
                    body: formData
                });

                const text = await response.text();

                // Buscar la redirección en la respuesta
                const match = text.match(/window\.location(?:\.href)?\s*=\s*['"]([^'"]+)['"]/);

                if (match) {
                    // Verificar si hay una cookie de redirección después del login
                    const redirectCookie = document.cookie
                        .split('; ')
                        .find(row => row.startsWith('redirect_after_login='));

                    if (redirectCookie) {
                        // Extraer el valor de la cookie
                        const redirectUrl = decodeURIComponent(redirectCookie.split('=')[1]);
                        // Eliminar la cookie
                        document.cookie = 'redirect_after_login=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';
                        // Redirigir a la URL guardada
                        window.location.href = redirectUrl;
                    } else {
                        // Si no hay cookie de redirección, usar la URL de la respuesta
                        window.location.href = match[1];
                    }
                } else if (text.includes("Datos ingresados incorrectos")) {
                    errorMessage.innerHTML = `
                  <div class="d-flex align-items-center justify-content-between gap-3">
                      <div class="alert alert-warning mb-0 flex-grow-1" role="alert">
                          <div class="d-flex align-items-center">
                              <i class="bi bi-exclamation-triangle-fill me-2"></i>
                              <span>¡Credenciales incorrectas!</span>
                          </div>
                      </div>
                      <button type="button" class="btn btn-light" data-bs-toggle="modal" data-bs-target="#reportModal">
                          <i class="bi bi-exclamation-triangle-fill me-2"></i>
                      </button>
                  </div>`;
                } else {
                    // Si no hay redirección ni error conocido, mostrar error genérico
                    errorMessage.innerHTML = `
                    <div class="d-flex align-items-center justify-content-between gap-3">
                      <div class="alert alert-warning mb-0 flex-grow-1" role="alert">
                          <div class="d-flex align-items-center">
                              <i class="bi bi-exclamation-triangle-fill me-2"></i>
                              <span>Error en el inicio de sesión!</span>
                          </div>
                      </div>
                      <button type="button" class="btn btn-light" data-bs-toggle="modal" data-bs-target="#reportModal">
                          <i class="bi bi-exclamation-triangle-fill me-2"></i>
                      </button>
                  </div>`;
                }
            } catch (error) {
                console.error('Error:', error);
                errorMessage.innerHTML = `
                <div class="d-flex align-items-center justify-content-between gap-3">
                      <div class="alert alert-warning mb-0 flex-grow-1" role="alert">
                          <div class="d-flex align-items-center">
                              <i class="bi bi-exclamation-triangle-fill me-2"></i>
                              <span>Error de conexión</span>
                          </div>
                      </div>
                      <button type="button" class="btn btn-light" data-bs-toggle="modal" data-bs-target="#reportModal">
                          <i class="bi bi-exclamation-triangle-fill me-2"></i>
                      </button>
                  </div>`;
            }
        });
        // Modal HTML
        document.body.insertAdjacentHTML('beforeend', `
        <div class="modal fade" id="reportModal" tabindex="-1" aria-labelledby="reportModalLabel" aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="reportModalLabel">Reportar Problema</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">

                     <div class="mb-3">
    <label for="reportEmail" class="form-label">Email</label>
    <select class="form-control" id="reportEmail">
        <option value="">Seleccione un email</option>
        <?php
        if ($conex) {
            $query = "SELECT DISTINCT id , email FROM tb_user_tqw WHERE email IS NOT NULL AND vigente = 'Si' AND email != '' ORDER BY email";
            $result = $conex->query($query);

            if ($result) {
                while($row = $result->fetch_assoc()) {
                    echo "<option value='" . htmlspecialchars($row['id']) . "'>" . htmlspecialchars($row['email']) . "</option>";
                }
            }
        }
        ?>
    </select>
</div>

                        <div class="mb-3">
                            <label for="reportMessage" class="form-label">Motivo</label>
                            <textarea class="form-control" id="reportMessage" rows="3"></textarea>
                        </div>
                        <div class="mb-3">
                            <label for="reportObservation" class="form-label">Observación</label>
                            <textarea class="form-control" id="reportObservation" rows="2"></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cerrar</button>
                        <button type="button" class="btn btn-primary">Enviar</button>
                    </div>
                </div>
            </div>
        </div>
    `);

        // Funcionalidad para mostrar/ocultar contraseña
        const passwordInput = document.getElementById('passs');
        const passwordVisibility = document.getElementById('password-visibility');

        if (passwordVisibility) {
            passwordVisibility.addEventListener('click', function() {
                const type = passwordInput.getAttribute('type') === 'password' ? 'text' : 'password';
                passwordInput.setAttribute('type', type);

                // Cambiar el ícono
                const eyeIcon = passwordVisibility.querySelector('.bi-eye');
                const eyeSlashIcon = passwordVisibility.querySelector('.bi-eye-slash');

                if (eyeIcon && eyeSlashIcon) {
                    eyeIcon.style.display = type === 'password' ? 'inline' : 'none';
                    eyeSlashIcon.style.display = type === 'password' ? 'none' : 'inline';
                }
            });
        }
    });


// Modificar el código del evento click del modal
document.addEventListener('DOMContentLoaded', function() {
    // Esperar a que el modal esté en el DOM
    setTimeout(() => {
        const submitButton = document.querySelector('#reportModal .btn-primary');
        if (submitButton) {
            submitButton.addEventListener('click', async function() {
                try {
                    const formData = {
                        observaciones: document.getElementById('reportMessage').value || '',
                        modulo: 'LOGIN',
                        complemento: document.getElementById('reportObservation').value || '',
                        id_tecnico: document.getElementById('reportEmail').value || ''
                    };

                    // Validar campos requeridos
                    if (!formData.id_tecnico || !formData.observaciones) {
                        alert('Por favor complete los campos requeridos');
                        return;
                    }

                    const response = await fetch('save_support_ticket.php', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(formData)
                    });

                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }

                    const data = await response.json();

                    if (data.success) {
                        alert('Ticket de soporte registrado exitosamente');
                        // Cerrar el modal usando Bootstrap
                        const modalElement = document.getElementById('reportModal');
                        const modal = bootstrap.Modal.getInstance(modalElement);
                        modal.hide();

                        // Limpiar el formulario
                        document.getElementById('reportMessage').value = '';
                        document.getElementById('reportObservation').value = '';
                        document.getElementById('reportEmail').value = '';

                        // Recargar la página actual
                        window.location.reload();
                    } else {
                        alert('Error al registrar el ticket: ' + (data.message || 'Error desconocido'));
                    }
                } catch (error) {
                    console.error('Error:', error);
                    alert('Error al enviar el ticket: ' + error.message);
                }
            });
        }
    }, 500); // Dar tiempo a que el modal se inserte en el DOM
});

    </script>


</body>

</html>