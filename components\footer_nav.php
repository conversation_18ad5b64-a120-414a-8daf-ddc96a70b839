<?php
/**
 * Footer de navegación estandarizado - Versión optimizada sin efectos para evitar parpadeos
 * Este archivo contiene el componente de navegación de pie de página para todas las interfaces
 */

// Determinar la página actual para resaltar el icono correspondiente
$currentPage = basename($_SERVER['PHP_SELF']);

// Acceder a la conexión a la base de datos para los combobox
if (!isset($conex) && file_exists(__DIR__ . '/../DatabaseConnection.php')) {
    try {
        require_once __DIR__ . '/../DatabaseConnection.php';
        $db = DatabaseConnection::getInstance();
        $conex = $db->getConnection();
    } catch (Exception $e) {
        // Error silencioso para no afectar la carga visual
    }
}

// Cargar estilos esenciales directamente para reducir solicitudes HTTP
// La estrategia es minimizar el código visual dejando solo lo esencial
echo '<style>
.form-panel{position:fixed;bottom:-100%;left:0;width:100%;height:80vh;background-color:#1c1f2c;box-shadow:0 -5px 15px rgba(0,0,0,.3);transition:all .3s ease;z-index:1000;padding:20px;overflow-y:auto;border-radius:20px 20px 0 0}.form-panel.active{bottom:0;z-index:1001}.form-overlay{position:fixed;top:0;left:0;right:0;bottom:0;background-color:rgba(0,0,0,.5);z-index:999;opacity:0;visibility:hidden;transition:all .3s ease}.form-overlay.active{opacity:1;visibility:visible}
</style>';

// Incluir los archivos CSS específicos
echo '<link rel="stylesheet" href="css/form_select_fix.css">';
echo '<link rel="stylesheet" href="css/carrito_compra.css">';
echo '<link rel="stylesheet" href="css/searchable_select.css">'; // Nuevo CSS para select con búsqueda

// Cargar script universal y prioritario para el footer
echo '<script>
// Cargar el script universal del footer con prioridad máxima
(function() {
    var script = document.createElement("script");
    script.src = "js/footer_universal.js?" + new Date().getTime(); // Timestamp para evitar caché
    script.async = false; // Asegurar ejecución secuencial
    document.body.appendChild(script);

    // Cargar scripts adicionales después del script universal
    setTimeout(function() {
        // Función para cargar scripts evitando duplicados
        function loadScript(src) {
            if (document.querySelector(\'script[src="\' + src + \'"]\')) return;
            var script = document.createElement("script");
            script.src = src;
            document.body.appendChild(script);
        }

        // Cargar scripts esenciales pero con menor prioridad que el universal
        loadScript("js/footer-quick.js");
        // Remover la carga duplicada de form_materiales
    }, 100);
})();
</script>';
?>

<!-- Footer Navigation - Versión simplificada para carga inmediata -->
<nav class="bottom-nav">
  <!-- Tres iconos a la izquierda del botón + -->
  <a href="activity_dashboard.php" target="_self" class="nav-item <?php echo ($currentPage == 'activity_dashboard.php') ? 'active' : ''; ?>" data-page="activity_dashboard">
    <i class="bi bi-grid"></i>
  </a>
  <a href="charts_dashboard.php" target="_self" class="nav-item <?php echo ($currentPage == 'charts_dashboard.php') ? 'active' : ''; ?>" data-page="charts_dashboard">
    <i class="bi bi-activity"></i>
  </a>
  <a href="calidad_reactiva.php" target="_self" class="nav-item <?php echo ($currentPage == 'calidad_reactiva.php') ? 'active' : ''; ?>" data-page="calidad_reactiva">
    <i class="bi bi-file-text"></i>
  </a>

  <!-- Botón + en el centro -->
  <div class="add-button-container">
    <!-- Menú flotante -->
    <div class="floating-menu" id="floatingMenu">
      <a href="#" class="floating-menu-item" id="formMateriales">
        <i class="bi bi-box-seam"></i> Formulario Materiales
      </a>
      <!-- Opciones temporalmente ocultas pero mantenidas en el código para futura activación -->
      <a href="#" class="floating-menu-item" id="formRevision" style="display: none;">
        <i class="bi bi-clipboard-check"></i> Formulario Revisión
      </a>
      <a href="#" class="floating-menu-item" id="formSoporte" style="display: none;">
        <i class="bi bi-headset"></i> Formulario Soporte
      </a>
    </div>
    <!-- Botón + -->
    <div class="nav-item add-button" id="addButton">
      <i class="bi bi-plus"></i>
    </div>
  </div>

  <!-- Tres iconos a la derecha del botón + -->
  <a href="javascript:void(0)" class="nav-item" id="ticketButton" data-page="tickets">
    <i class="bi bi-ticket"></i>
  </a>
  <a href="Tecnico_Home_LOGIS_TEST_V2.php" target="_self" class="nav-item <?php echo ($currentPage == 'Tecnico_Home_LOGIS_TEST_V2.php' || $currentPage == 'Tecnico_Home_LOGIS_TEST.php') ? 'active' : ''; ?>" data-page="home">
    <i class="bi bi-house"></i>
  </a>
  <a href="logout.php" class="nav-item" id="logoutButton" data-page="logout">
    <i class="bi bi-box-arrow-right"></i>
  </a>
</nav>

<!-- Overlays simplificados -->
<div class="menu-overlay" id="menuOverlay"></div>
<div class="form-overlay" id="formOverlay"></div>

<!-- Formulario de Solicitud de Materiales (usando el estilo form-panel) -->
<div class="form-panel" id="solicitudMaterialesPanel">
    <div class="form-container">
        <div class="form-header">
            <h2 class="form-title"><i class="bi bi-box-seam"></i> Solicitud de Materiales</h2>
            <button type="button" class="close-form-btn" title="Cerrar formulario" aria-label="Cerrar formulario"><i class="bi bi-x-lg"></i></button>
        </div>

        <form id="solicitudMaterialesForm">
            <div class="form-group">
                <label for="tipo_material_select" class="form-label">Tipo de Material</label>
                <select id="tipo_material_select" class="form-select" required>
                    <option value="">Seleccione un tipo de material</option>
                    <?php
                        // Reutilizar la conexión a la base de datos existente
                        if (isset($conex)) {
                            $tipos_query = "SELECT DISTINCT `Tipo Material` FROM tp_logistica_mat_oracle ORDER BY `Tipo Material` ASC";
                            $tipos = $conex->query($tipos_query);
                            while ($row = $tipos->fetch_assoc()) {
                                echo "<option value='" . htmlspecialchars($row['Tipo Material']) . "'>" . htmlspecialchars($row['Tipo Material']) . "</option>";
                            }
                        }
                    ?>
                </select>
            </div>

            <div class="form-group">
                <label for="familia_select" class="form-label">Familia</label>
                <select id="familia_select" class="form-select searchable-select" required disabled>
                    <option value="">Seleccione una familia</option>
                </select>
            </div>

            <div class="form-group">
                <label for="subfamilia_select" class="form-label">Sub Familia</label>
                <select id="subfamilia_select" class="form-select searchable-select" required disabled>
                    <option value="">Seleccione una subfamilia</option>
                </select>
            </div>

            <div class="form-group">
                <label for="icon_bootstrap" class="form-label">Material</label>
                <div style="display: flex; gap: 5px;">
                    <select id="icon_bootstrap" class="form-select searchable-select" required disabled>
                        <option value="">Seleccione un material</option>
                    </select>
                    <button type="button" class="btn-reset" style="padding: 10px; margin: 0; background-color: rgba(255, 255, 255, 0.1); color: var(--text-color); border: none; border-radius: 10px; cursor: pointer; transition: all 0.3s ease;" onclick="resetSearchableSelect()" title="Limpiar campo">
                        <i class="bi bi-trash"></i>
                    </button>
                </div>
            </div>

            <div class="form-group">
                <label for="cantidad" class="form-label">Cantidad</label>
                <input type="number" id="cantidad" class="form-control" min="1" max="500" required>
            </div>

            <div class="form-group">
                <button type="button" id="agregar_al_carrito" class="btn-submit" style="width: 100%;">
                    <i class="bi bi-plus-circle"></i> Agregar al Carrito
                </button>
            </div>

            <?php if (isset($id_usuario) && ($id_usuario == 73 || $id_usuario == 160)) : ?>
            <div class="form-group" style="margin-top: 20px;">
                <h3 style="font-size: 1.2rem; margin-bottom: 15px; color: var(--accent-color);">ASIGNACIÓN</h3>

                <label for="tecnicoTraspaso" class="form-label">Técnico a quien transfiere</label>
                <select id="tecnicoTraspaso" class="form-select" required>
                    <option value="">Seleccione el técnico a transferir</option>
                    <?php
                        if (isset($dotacion) && $dotacion->num_rows > 0) {
                            while ($row = mysqli_fetch_assoc($dotacion)) {
                                echo "<option value=\"" . $row["id"] . "\">" . $row["nombre"] . "</option>";
                            }
                        }
                    ?>
                </select>
            </div>

            <div class="form-group">
                <label for="seriadoCarrito" class="form-label">Seriado</label>
                <input type="text" id="seriadoCarrito" class="form-control" required>
            </div>
            <?php endif; ?>

            <div class="form-group" style="margin-top: 20px;">
                <h3 class="cart-title"><i class="bi bi-cart3"></i> CARRITO DE MATERIALES</h3>
                <div class="cart-container">
                    <div id="carrito" style="max-height: 200px; overflow-y: auto; margin-bottom: 0; padding: 8px;">
                        <!-- Aquí se mostrarán las selecciones guardadas -->
                        <div class="cart-empty">
                            <i class="bi bi-cart"></i>
                            <p>No hay items en el carrito</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="form-buttons">
                <button type="button" class="btn-cancel">Cancelar</button>
                <button type="button" class="btn-submit" onclick="confirmarSolicitud()">
                    <i class="bi bi-cursor"></i> Confirmar Solicitud
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Formulario de Revisión -->
<div class="form-panel" id="revisionFormPanel">
    <div class="form-container">
        <div class="form-header">
            <h2 class="form-title"><i class="bi bi-clipboard-check"></i> Formulario de Revisión</h2>
            <button type="button" class="close-form-btn" title="Cerrar formulario" aria-label="Cerrar formulario"><i class="bi bi-x-lg"></i></button>
        </div>

        <form id="revisionForm">
            <div class="form-group">
                <label for="tipo_revision" class="form-label">Tipo de Revisión</label>
                <select id="tipo_revision" class="form-select" required>
                    <option value="">Seleccione un tipo</option>
                    <option value="equipos">Equipos</option>
                    <option value="instalacion">Instalación</option>
                    <option value="mantenimiento">Mantenimiento</option>
                </select>
            </div>

            <div class="form-group">
                <label for="descripcion_revision" class="form-label">Descripción</label>
                <textarea id="descripcion_revision" class="form-control" rows="4" required placeholder="Describa los detalles de la revisión..."></textarea>
            </div>

            <div class="form-group">
                <label for="prioridad_revision" class="form-label">Prioridad</label>
                <select id="prioridad_revision" class="form-select" required>
                    <option value="">Seleccione la prioridad</option>
                    <option value="baja">Baja</option>
                    <option value="media">Media</option>
                    <option value="alta">Alta</option>
                    <option value="urgente">Urgente</option>
                </select>
            </div>

            <div class="form-buttons">
                <button type="button" class="btn-cancel">Cancelar</button>
                <button type="button" class="btn-submit">
                    <i class="bi bi-send"></i> Enviar Solicitud
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Formulario de Soporte -->
<div class="form-panel" id="soporteFormPanel">
    <div class="form-container">
        <div class="form-header">
            <h2 class="form-title"><i class="bi bi-headset"></i> Solicitud de Soporte</h2>
            <button type="button" class="close-form-btn" title="Cerrar formulario" aria-label="Cerrar formulario"><i class="bi bi-x-lg"></i></button>
        </div>

        <form id="soporteForm">
            <div class="form-group">
                <label for="categoria_soporte" class="form-label">Categoría</label>
                <select id="categoria_soporte" class="form-select" required>
                    <option value="">Seleccione una categoría</option>
                    <option value="tecnico">Soporte Técnico</option>
                    <option value="sistema">Sistema</option>
                    <option value="acceso">Problemas de Acceso</option>
                    <option value="otro">Otro</option>
                </select>
            </div>

            <div class="form-group">
                <label for="asunto_soporte" class="form-label">Asunto</label>
                <input type="text" id="asunto_soporte" class="form-control" required placeholder="Resumen breve del problema">
            </div>

            <div class="form-group">
                <label for="descripcion_soporte" class="form-label">Descripción del Problema</label>
                <textarea id="descripcion_soporte" class="form-control" rows="5" required placeholder="Describa detalladamente el problema que está experimentando..."></textarea>
            </div>

            <div class="form-group">
                <label for="urgencia_soporte" class="form-label">Nivel de Urgencia</label>
                <select id="urgencia_soporte" class="form-select" required>
                    <option value="">Seleccione el nivel de urgencia</option>
                    <option value="baja">Baja - Puede esperar</option>
                    <option value="media">Media - Afecta el trabajo</option>
                    <option value="alta">Alta - Impide trabajar</option>
                    <option value="critica">Crítica - Emergencia</option>
                </select>
            </div>

            <div class="form-buttons">
                <button type="button" class="btn-cancel">Cancelar</button>
                <button type="button" class="btn-submit">
                    <i class="bi bi-send"></i> Enviar Solicitud
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Estilos para el nuevo modal de cierre de sesión -->
<style>
.logout-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  display: none; /* Oculto por defecto */
  align-items: center;
  justify-content: center;
  z-index: 2000;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.logout-modal-content {
  background-color: var(--card-bg, #1c1f2c);
  color: var(--text-color, #ffffff);
  padding: 30px;
  border-radius: 15px;
  box-shadow: 0 5px 25px rgba(0, 0, 0, 0.4);
  width: 90%;
  max-width: 350px;
  text-align: center;
  transform: scale(0.9);
  transition: transform 0.3s ease;
}

.logout-modal.show {
  display: flex;
  opacity: 1;
}

.logout-modal.show .logout-modal-content {
  transform: scale(1);
}

.logout-modal-content h3 {
  margin-top: 0;
  margin-bottom: 15px;
  color: var(--accent-color, #00e1fd);
  font-weight: 600;
}

.logout-modal-content p {
  margin-bottom: 30px;
  opacity: 0.9;
}

.logout-modal-buttons {
  display: flex;
  gap: 15px;
  justify-content: center;
}

.logout-modal-buttons button {
  padding: 12px 25px;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
}

.logout-modal-buttons .btn-confirm {
  background-color: var(--accent-color, #00e1fd);
  color: var(--primary-bg, #1e2746);
}

.logout-modal-buttons .btn-confirm:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 15px rgba(0, 225, 253, 0.3);
}

.logout-modal-buttons .btn-cancel {
  background-color: rgba(255, 255, 255, 0.1);
  color: var(--text-color, #ffffff);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.logout-modal-buttons .btn-cancel:hover {
  background-color: rgba(255, 255, 255, 0.2);
}
</style>

<!-- Modal de confirmación de cierre de sesión (HTML sin cambios) -->
<div class="logout-modal" id="logoutModal">
  <div class="logout-modal-content">
    <h3>Cerrar Sesión</h3>
    <p>¿Está seguro que desea cerrar la sesión?</p>
    <div class="logout-modal-buttons">
      <button id="cancelLogout" class="btn-cancel">Cancelar</button>
      <button id="confirmLogout" class="btn-confirm">Cerrar Sesión</button>
    </div>
  </div>
</div>

<!-- Incluir estilos directamente para evitar solicitudes adicionales -->
<style>
/* Estilos mínimos para form-panels */
.form-panel {
  position: fixed;
  bottom: -100%;
  left: 0;
  width: 100%;
  height: 80vh;
  background-color: var(--card-bg, #1c1f2c);
  box-shadow: 0 -5px 15px rgba(0, 0, 0, 0.3);
  transition: all 0.3s ease;
  z-index: 1000;
  padding: 20px;
  overflow-y: auto;
  border-radius: 20px 20px 0 0;
}

.form-panel.active {
  bottom: 0;
  z-index: 1001;
}

.form-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 999;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.form-overlay.active {
  opacity: 1;
  visibility: visible;
}

/* Estilos específicos para los elementos select en el formulario de materiales */
#solicitudMaterialesPanel .form-select {
  background-color: rgba(255, 255, 255, 0.1);
  color: var(--text-color, #ffffff);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

#solicitudMaterialesPanel .form-select option {
  background-color: var(--card-bg, #2a3353) !important;
  color: var(--text-color, #ffffff) !important;
  padding: 12px 15px !important;
  font-size: 14px !important;
}

#solicitudMaterialesPanel .form-select option:checked {
  background-color: var(--accent-color, #00e1fd) !important;
  color: var(--primary-bg, #1e2746) !important;
  font-weight: bold !important;
}

/* Estilo para el botón de reset */
.btn-reset:hover {
  background-color: rgba(255, 255, 255, 0.2) !important;
}
</style>

<!-- Scripts prioritarios cargados inmediatamente -->
<script>
// Configuración inmediata sin eventos ni retrasos
(function() {
    // Configurar todos los enlaces para que abran en la misma pestaña
    var navLinks = document.querySelectorAll(".bottom-nav a:not([target])");
    for (var i = 0; i < navLinks.length; i++) {
        navLinks[i].setAttribute("target", "_self");
    }

    // Cargar scripts esenciales
    function loadScript(src) {
        if (!document.querySelector('script[src="' + src + '"]')) {
            var script = document.createElement('script');
            script.src = src;
            document.body.appendChild(script);
        }
    }

    // Registrar historial
    if (window.localStorage) {
        try {
            var currentPage = window.location.href;
            var previousPages = JSON.parse(localStorage.getItem("navigationHistory") || "[]");
            if (previousPages.indexOf(currentPage) === -1) {
                previousPages.push(currentPage);
                if (previousPages.length > 5) previousPages = previousPages.slice(-5);
                localStorage.setItem("navigationHistory", JSON.stringify(previousPages));
            }
        } catch(e) { /* Ignorar errores de localStorage */ }
    }

    // Cargar scripts en segundo plano para no bloquear la interfaz
    setTimeout(function() {
        // Cargar primero el script de corrección del carrito para asegurar que esté disponible
        var cartScript = document.createElement('script');
        cartScript.src = 'js/add_to_cart_direct.js?' + new Date().getTime(); // Añadir timestamp para evitar caché
        cartScript.onload = function() {
            console.log('Script del carrito cargado correctamente');

            // Después cargar los demás scripts en orden específico con manejo de dependencias
            var searchableScript = document.createElement('script');
            searchableScript.src = 'js/searchable_select.js?' + new Date().getTime();
            searchableScript.onload = function() {
                console.log('Script searchable_select.js cargado correctamente');

                // Cargar el script de cascada mejorado
                setTimeout(function() {
                    var cascadaFixedScript = document.createElement('script');
                    cascadaFixedScript.src = 'js/form_materiales_cascada_fixed.js?' + new Date().getTime();
                    cascadaFixedScript.onload = function() {
                        console.log('Script form_materiales_cascada_fixed.js cargado correctamente');
                        
                        // Asegurar que el script se ejecute en el contexto correcto
                        if (typeof inicializarModuloCascada === 'function') {
                            console.log('[footer_nav] Función inicializarModuloCascada disponible');
                        }
                    };
                    document.body.appendChild(cascadaFixedScript);
                }, 500); // Aumentar el tiempo para dar más estabilidad


            };
            document.body.appendChild(searchableScript);

            loadScript('js/ticket-tooltip.js');
            loadScript('js/form_select_fix.js'); // Script para corregir los estilos de los select
        };
        document.body.appendChild(cartScript);

        // Configuración temporal para mostrar solo el formulario de materiales
        console.log('Configuración temporal: solo formulario de materiales habilitado');

        // Modificar el comportamiento del botón + para abrir directamente el formulario de materiales
        var addButton = document.getElementById('addButton');
        var floatingMenu = document.getElementById('floatingMenu');
        var formMaterialesPanel = document.getElementById('solicitudMaterialesPanel');
        var formOverlay = document.getElementById('formOverlay');

        if (addButton) {
            // Configurar el comportamiento del botón + para mostrar el menú flotante con opciones
            addButton.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation(); // Detener propagación para evitar conflictos

                // Mostrar el menú flotante con las opciones
                var floatingMenu = document.getElementById('floatingMenu');
                var menuOverlay = document.getElementById('menuOverlay');

                if (floatingMenu && menuOverlay) {
                    floatingMenu.classList.toggle('active');
                    menuOverlay.classList.toggle('active');

                    // Cambiar icono
                    var icon = addButton.querySelector('i');
                    if (icon) {
                        if (floatingMenu.classList.contains('active')) {
                            icon.className = 'bi bi-x';
                        } else {
                            icon.className = 'bi bi-plus';
                        }
                    }
                }

                return false;
            }, true); // Usar captura para asegurar que este evento se ejecute primero
        }
    }, 10);

    // Configurar los enlaces del menú flotante para abrir los formularios correspondientes
    var formMaterialesLink = document.getElementById('formMateriales');
    var formRevisionLink = document.getElementById('formRevision');
    var formSoporteLink = document.getElementById('formSoporte');

    var formMaterialesPanel = document.getElementById('solicitudMaterialesPanel');
    var revisionFormPanel = document.getElementById('revisionFormPanel');
    var soporteFormPanel = document.getElementById('soporteFormPanel');
    var formOverlay = document.getElementById('formOverlay');
    var menuOverlay = document.getElementById('menuOverlay');
    var floatingMenu = document.getElementById('floatingMenu');

    // Función para mostrar un panel de formulario
    function showFormPanel(panel) {
        // Ocultar el menú flotante
        if (floatingMenu) floatingMenu.classList.remove('active');
        if (menuOverlay) menuOverlay.classList.remove('active');

        // Restaurar el icono del botón +
        var addButton = document.getElementById('addButton');
        if (addButton) {
            var icon = addButton.querySelector('i');
            if (icon) icon.className = 'bi bi-plus';
        }

        // Mostrar el panel y el overlay
        if (panel && formOverlay) {
            panel.classList.add('active');
            formOverlay.classList.add('active');
            formOverlay.style.opacity = '1';
            formOverlay.style.visibility = 'visible';
        }
    }

    // Configurar los botones de cierre de formularios
    document.querySelectorAll('.close-form-btn, .btn-cancel').forEach(function(btn) {
        btn.addEventListener('click', function() {
            // Ocultar todos los paneles de formulario
            document.querySelectorAll('.form-panel').forEach(function(panel) {
                panel.classList.remove('active');
            });

            // Ocultar el overlay
            if (formOverlay) {
                formOverlay.classList.remove('active');
                formOverlay.style.opacity = '0';
                formOverlay.style.visibility = 'hidden';
            }
        });
    });

    // Configurar el overlay para cerrar formularios al hacer clic
    if (formOverlay) {
        formOverlay.addEventListener('click', function() {
            document.querySelectorAll('.form-panel').forEach(function(panel) {
                panel.classList.remove('active');
            });
            formOverlay.classList.remove('active');
            formOverlay.style.opacity = '0';
            formOverlay.style.visibility = 'hidden';
        });
    }

    // Configurar los enlaces del menú flotante
    if (formMaterialesLink && formMaterialesPanel) {
        formMaterialesLink.addEventListener('click', function(e) {
            e.preventDefault();
            showFormPanel(formMaterialesPanel);
        });
    }

    if (formRevisionLink && revisionFormPanel) {
        formRevisionLink.addEventListener('click', function(e) {
            e.preventDefault();
            showFormPanel(revisionFormPanel);
        });
    }

    if (formSoporteLink && soporteFormPanel) {
        formSoporteLink.addEventListener('click', function(e) {
            e.preventDefault();
            showFormPanel(soporteFormPanel);
        });
    }

    // Inicializar los selects con búsqueda cuando el DOM esté completamente cargado
    document.addEventListener('DOMContentLoaded', function() {
    const logoutButton = document.getElementById('logoutButton');
    const logoutModal = document.getElementById('logoutModal');
    const cancelLogout = document.getElementById('cancelLogout');
    const confirmLogout = document.getElementById('confirmLogout');

    if (logoutButton) {
        logoutButton.addEventListener('click', function(e) {
            e.preventDefault();
            if (logoutModal) {
                logoutModal.classList.add('show');
            }
        });
    }

    function hideModal() {
        if (logoutModal) {
            logoutModal.classList.remove('show');
        }
    }

    if (cancelLogout) {
        cancelLogout.addEventListener('click', hideModal);
    }

    if (confirmLogout) {
        confirmLogout.addEventListener('click', function() {
            window.location.href = 'logout.php';
        });
    }

    // Cerrar el modal si se hace clic fuera de él
    window.addEventListener('click', function(e) {
        if (e.target === logoutModal) {
            hideModal();
        }
    });
});
})();
</script>